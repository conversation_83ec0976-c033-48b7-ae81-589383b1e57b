#include "clock_styles.h"
#include "clock_display.h"
#include <stdio.h>
#include <string.h>

// 全局样式变量
uint8_t g_CurrentStyle = STYLE_MODERN;

// 样式管理函数
void Clock_SetStyle(uint8_t style)
{
    if (style <= STYLE_CLASSIC) {
        g_CurrentStyle = style;
        printf("Style changed to: %s\r\n", 
               style == STYLE_MODERN ? "Modern" : "Classic");
    }
}

uint8_t Clock_GetStyle(void)
{
    return g_CurrentStyle;
}

void Clock_InitStyle(void)
{
    g_CurrentStyle = STYLE_MODERN;
}

// 颜色获取函数
uint16_t GetBgColor(void)
{
    return (g_CurrentStyle == STYLE_MODERN) ? MODERN_BG_COLOR : CLASSIC_BG_COLOR;
}

uint16_t GetTextColor(void)
{
    return (g_CurrentStyle == STYLE_MODERN) ? MODERN_TEXT_COLOR : CLASSIC_TEXT_COLOR;
}

uint16_t GetAccentColor(void)
{
    return (g_CurrentStyle == STYLE_MODERN) ? MODERN_ACCENT_COLOR : CLASSIC_ACCENT_COLOR;
}

uint16_t GetLightColor(void)
{
    return (g_CurrentStyle == STYLE_MODERN) ? MODERN_LIGHT_COLOR : CLASSIC_LIGHT_COLOR;
}

uint16_t GetWarningColor(void)
{
    return (g_CurrentStyle == STYLE_MODERN) ? MODERN_WARNING_COLOR : CLASSIC_WARNING_COLOR;
}

// 现代简约风格主界面
void Clock_DrawModernMain(DateTime* time)
{
    char buffer[50];
    
    // 清屏并设置背景
    LCD_Clear(MODERN_BG_COLOR);
    
    // 绘制日期卡片
    DrawCard(20, 20, 200, 40, MODERN_CARD_COLOR, MODERN_LIGHT_COLOR);
    POINT_COLOR = MODERN_TEXT_COLOR;
    BACK_COLOR = MODERN_CARD_COLOR;
    sprintf(buffer, "%04d-%02d-%02d %s", 
            time->year, time->month, time->day, GetWeekdayName(time->week));
    LCD_ShowString(30, 30, 200, 16, 16, (u8*)buffer);
    
    // 绘制大号时间显示
    POINT_COLOR = MODERN_ACCENT_COLOR;
    BACK_COLOR = MODERN_BG_COLOR;
    sprintf(buffer, "%02d:%02d", time->hour, time->minute);
    LCD_ShowString(60, 80, 200, 48, 48, (u8*)buffer);
    
    // 绘制秒数
    POINT_COLOR = MODERN_TEXT_COLOR;
    sprintf(buffer, "%02d", time->second);
    LCD_ShowString(180, 100, 50, 24, 24, (u8*)buffer);
    
    // 绘制功能按钮
    Clock_DrawModernButton(20, 160, 60, 30, "ALARM", 0);
    Clock_DrawModernButton(90, 160, 60, 30, "SET", 0);
    Clock_DrawModernButton(160, 160, 60, 30, "STYLE", 0);
}

// 现代简约风格卡片
void Clock_DrawModernCard(int16_t x, int16_t y, int16_t width, int16_t height, const char* title)
{
    // 绘制卡片背景
    DrawCard(x, y, width, height, MODERN_CARD_COLOR, MODERN_LIGHT_COLOR);
    
    // 绘制标题
    if (title) {
        POINT_COLOR = MODERN_TEXT_COLOR;
        BACK_COLOR = MODERN_CARD_COLOR;
        LCD_ShowString(x + 10, y + 5, width - 20, 16, 16, (u8*)title);
    }
}

// 现代简约风格按钮
void Clock_DrawModernButton(int16_t x, int16_t y, int16_t width, int16_t height, 
                           const char* text, uint8_t selected)
{
    uint16_t bg_color = selected ? MODERN_ACCENT_COLOR : MODERN_LIGHT_COLOR;
    uint16_t text_color = selected ? WHITE : MODERN_TEXT_COLOR;
    
    // 绘制圆角矩形按钮
    DrawRoundedRect(x, y, x + width, y + height, bg_color, BUTTON_RADIUS);
    
    // 绘制按钮文字
    POINT_COLOR = text_color;
    BACK_COLOR = bg_color;
    uint8_t text_x = x + (width - strlen(text) * 8) / 2;
    uint8_t text_y = y + (height - 16) / 2;
    LCD_ShowString(text_x, text_y, width, 16, 16, (u8*)text);
}

// 经典复古风格主界面
void Clock_DrawClassicMain(DateTime* time)
{
    // 清屏并设置背景
    LCD_Clear(CLASSIC_BG_COLOR);
    
    // 绘制木质边框
    Clock_DrawClassicWoodFrame(10, 10, 220, 300);
    
    // 绘制模拟时钟
    Clock_DrawClassicAnalogClock(time);
    
    // 绘制日期信息
    char buffer[50];
    POINT_COLOR = CLASSIC_TEXT_COLOR;
    BACK_COLOR = CLASSIC_BG_COLOR;
    sprintf(buffer, "%s, %s %d, %d", 
            GetWeekdayName(time->week), GetMonthName(time->month), 
            time->day, time->year);
    LCD_ShowString(30, 250, 200, 16, 16, (u8*)buffer);
    
    // 绘制数字时间
    sprintf(buffer, "%02d:%02d:%02d", time->hour, time->minute, time->second);
    LCD_ShowString(80, 270, 120, 16, 16, (u8*)buffer);
}

// 经典复古风格木质边框
void Clock_DrawClassicWoodFrame(int16_t x, int16_t y, int16_t width, int16_t height)
{
    // 外边框
    POINT_COLOR = CLASSIC_WOOD_COLOR;
    LCD_DrawRectangle(x, y, x + width, y + height);
    LCD_DrawRectangle(x + 1, y + 1, x + width - 1, y + height - 1);
    
    // 内边框
    LCD_DrawRectangle(x + 5, y + 5, x + width - 5, y + height - 5);
}

// 经典复古风格按钮
void Clock_DrawClassicButton(int16_t x, int16_t y, int16_t width, int16_t height, 
                            const char* text, uint8_t selected)
{
    uint16_t bg_color = selected ? CLASSIC_ACCENT_COLOR : CLASSIC_LIGHT_COLOR;
    uint16_t text_color = selected ? CLASSIC_TEXT_COLOR : CLASSIC_TEXT_COLOR;
    
    // 绘制立体按钮效果
    LCD_Fill(x, y, x + width, y + height, bg_color);
    POINT_COLOR = selected ? CLASSIC_WARNING_COLOR : CLASSIC_WOOD_COLOR;
    LCD_DrawRectangle(x, y, x + width, y + height);
    
    // 绘制按钮文字
    POINT_COLOR = text_color;
    BACK_COLOR = bg_color;
    uint8_t text_x = x + (width - strlen(text) * 8) / 2;
    uint8_t text_y = y + (height - 16) / 2;
    LCD_ShowString(text_x, text_y, width, 16, 16, (u8*)text);
}

// 通用辅助绘制函数
void DrawRoundedRect(int16_t x1, int16_t y1, int16_t x2, int16_t y2, 
                     uint16_t color, uint8_t radius)
{
    // 简化的圆角矩形实现
    LCD_Fill(x1 + radius, y1, x2 - radius, y2, color);
    LCD_Fill(x1, y1 + radius, x2, y2 - radius, color);
    
    // 绘制边框
    POINT_COLOR = color;
    LCD_DrawRectangle(x1, y1, x2, y2);
}

void DrawCard(int16_t x, int16_t y, int16_t width, int16_t height, 
              uint16_t bg_color, uint16_t border_color)
{
    // 绘制卡片背景
    LCD_Fill(x, y, x + width, y + height, bg_color);
    
    // 绘制边框
    POINT_COLOR = border_color;
    LCD_DrawRectangle(x, y, x + width, y + height);
}

// 图标绘制函数（简化实现）
void DrawIcon_Clock(int16_t x, int16_t y, uint16_t color)
{
    POINT_COLOR = color;
    LCD_Draw_Circle(x + 8, y + 8, 8);
    LCD_DrawLine(x + 8, y + 8, x + 8, y + 4);
    LCD_DrawLine(x + 8, y + 8, x + 12, y + 8);
}

void DrawIcon_Alarm(int16_t x, int16_t y, uint16_t color)
{
    POINT_COLOR = color;
    LCD_Draw_Circle(x + 8, y + 8, 6);
    LCD_DrawLine(x + 5, y + 3, x + 7, y + 5);
    LCD_DrawLine(x + 9, y + 5, x + 11, y + 3);
}

void DrawIcon_Settings(int16_t x, int16_t y, uint16_t color)
{
    POINT_COLOR = color;
    LCD_Draw_Circle(x + 8, y + 8, 4);
    for (int i = 0; i < 8; i++) {
        int angle = i * 45;
        int dx = (int)(6 * cos(angle * 3.14159 / 180));
        int dy = (int)(6 * sin(angle * 3.14159 / 180));
        LCD_DrawPoint(x + 8 + dx, y + 8 + dy);
    }
}

void DrawIcon_Back(int16_t x, int16_t y, uint16_t color)
{
    POINT_COLOR = color;
    LCD_DrawLine(x + 2, y + 8, x + 8, y + 2);
    LCD_DrawLine(x + 2, y + 8, x + 8, y + 14);
    LCD_DrawLine(x + 2, y + 8, x + 14, y + 8);
}
