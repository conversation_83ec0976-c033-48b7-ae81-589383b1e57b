#include "clock_styles.h"
#include "clock_display.h"
#include "key.h"
#include "uart_protocol.h"
#include <stdio.h>
#include <string.h>

// 现代简约风格的全局变量
static uint8_t g_ModernCurrentPage = PAGE_CLOCK;
static uint8_t g_ModernSelectedItem = 0;
static uint8_t g_ModernNeedRefresh = 1;
static uint8_t g_ModernEditMode = 0;

// 现代简约风格闹钟列表页面
void Clock_DrawModernAlarmList(void)
{
    char buffer[50];
    
    // 清屏
    LCD_Clear(MODERN_BG_COLOR);
    
    // 绘制标题卡片
    Clock_DrawModernCard(20, 20, 200, 40, "Alarm Management");
    
    // 绘制闹钟列表
    for (uint8_t i = 0; i < g_AlarmManager.count && i < 4; i++) {
        Alarm* alarm = &g_AlarmManager.alarms[i];
        uint8_t y_pos = 70 + i * 50;
        uint8_t selected = (g_ModernSelectedItem == i + 1);
        
        // 绘制闹钟卡片
        uint16_t card_color = alarm->enabled ? MODERN_CARD_COLOR : MODERN_LIGHT_COLOR;
        uint16_t border_color = selected ? MODERN_ACCENT_COLOR : MODERN_LIGHT_COLOR;
        
        DrawCard(20, y_pos, 200, 45, card_color, border_color);
        
        // 绘制闹钟时间
        POINT_COLOR = alarm->enabled ? MODERN_ACCENT_COLOR : MODERN_TEXT_COLOR;
        BACK_COLOR = card_color;
        sprintf(buffer, "%02d:%02d", alarm->hour, alarm->minute);
        LCD_ShowString(30, y_pos + 5, 100, 24, 24, (u8*)buffer);
        
        // 绘制闹钟名称
        POINT_COLOR = MODERN_TEXT_COLOR;
        LCD_ShowString(30, y_pos + 25, 150, 16, 16, (u8*)alarm->name);
        
        // 绘制状态指示
        POINT_COLOR = alarm->enabled ? MODERN_SUCCESS_COLOR : MODERN_WARNING_COLOR;
        LCD_ShowString(170, y_pos + 15, 40, 16, 16, 
                      (u8*)(alarm->enabled ? "ON" : "OFF"));
    }
    
    // 绘制添加按钮
    uint8_t add_selected = (g_ModernSelectedItem == g_AlarmManager.count + 1);
    Clock_DrawModernButton(20, 270, 80, 30, "+ ADD", add_selected);
    
    // 绘制返回按钮
    uint8_t back_selected = (g_ModernSelectedItem == 0);
    Clock_DrawModernButton(140, 270, 80, 30, "BACK", back_selected);
    
    g_ModernNeedRefresh = 0;
}

// 现代简约风格闹钟编辑页面
void Clock_DrawModernAlarmEdit(uint8_t index)
{
    char buffer[50];
    
    // 清屏
    LCD_Clear(MODERN_BG_COLOR);
    
    // 绘制标题
    const char* title = (index < g_AlarmManager.count) ? "Edit Alarm" : "New Alarm";
    Clock_DrawModernCard(20, 20, 200, 40, title);
    
    // 获取当前编辑的闹钟（使用临时变量或现有闹钟）
    Alarm* current_alarm = (index < g_AlarmManager.count) ? 
                          &g_AlarmManager.alarms[index] : NULL;
    
    // 绘制时间设置卡片
    DrawCard(20, 70, 200, 60, MODERN_CARD_COLOR, 
             (g_ModernSelectedItem == 1) ? MODERN_ACCENT_COLOR : MODERN_LIGHT_COLOR);
    
    POINT_COLOR = MODERN_TEXT_COLOR;
    BACK_COLOR = MODERN_CARD_COLOR;
    LCD_ShowString(30, 80, 100, 16, 16, (u8*)"Time:");
    
    if (current_alarm) {
        sprintf(buffer, "%02d:%02d:%02d", 
                current_alarm->hour, current_alarm->minute, current_alarm->second);
    } else {
        sprintf(buffer, "00:00:00");
    }
    
    POINT_COLOR = MODERN_ACCENT_COLOR;
    LCD_ShowString(30, 100, 150, 24, 24, (u8*)buffer);
    
    // 绘制重复设置卡片
    DrawCard(20, 140, 200, 40, MODERN_CARD_COLOR,
             (g_ModernSelectedItem == 2) ? MODERN_ACCENT_COLOR : MODERN_LIGHT_COLOR);
    
    POINT_COLOR = MODERN_TEXT_COLOR;
    BACK_COLOR = MODERN_CARD_COLOR;
    LCD_ShowString(30, 150, 100, 16, 16, (u8*)"Repeat:");
    
    if (current_alarm) {
        if (current_alarm->days == 0x7F) {
            strcpy(buffer, "Every day");
        } else if (current_alarm->days == 0x1F) {
            strcpy(buffer, "Weekdays");
        } else {
            strcpy(buffer, "Custom");
        }
    } else {
        strcpy(buffer, "Every day");
    }
    LCD_ShowString(100, 150, 100, 16, 16, (u8*)buffer);
    
    // 绘制名称设置卡片
    DrawCard(20, 190, 200, 40, MODERN_CARD_COLOR,
             (g_ModernSelectedItem == 3) ? MODERN_ACCENT_COLOR : MODERN_LIGHT_COLOR);
    
    POINT_COLOR = MODERN_TEXT_COLOR;
    BACK_COLOR = MODERN_CARD_COLOR;
    LCD_ShowString(30, 200, 100, 16, 16, (u8*)"Name:");
    
    if (current_alarm) {
        LCD_ShowString(100, 200, 100, 16, 16, (u8*)current_alarm->name);
    } else {
        LCD_ShowString(100, 200, 100, 16, 16, (u8*)"New Alarm");
    }
    
    // 绘制操作按钮
    Clock_DrawModernButton(20, 250, 60, 30, "SAVE", 
                          (g_ModernSelectedItem == 4));
    Clock_DrawModernButton(90, 250, 60, 30, "CANCEL", 
                          (g_ModernSelectedItem == 0));
    Clock_DrawModernButton(160, 250, 60, 30, "DELETE", 
                          (g_ModernSelectedItem == 5));
    
    g_ModernNeedRefresh = 0;
}

// 现代简约风格时间设置页面
void Clock_DrawModernTimeSet(DateTime* time)
{
    char buffer[50];
    
    // 清屏
    LCD_Clear(MODERN_BG_COLOR);
    
    // 绘制标题
    Clock_DrawModernCard(20, 20, 200, 40, "Time Settings");
    
    // 绘制日期设置卡片
    DrawCard(20, 70, 200, 60, MODERN_CARD_COLOR,
             (g_ModernSelectedItem == 1) ? MODERN_ACCENT_COLOR : MODERN_LIGHT_COLOR);
    
    POINT_COLOR = MODERN_TEXT_COLOR;
    BACK_COLOR = MODERN_CARD_COLOR;
    LCD_ShowString(30, 80, 100, 16, 16, (u8*)"Date:");
    
    sprintf(buffer, "%04d-%02d-%02d", time->year, time->month, time->day);
    POINT_COLOR = MODERN_ACCENT_COLOR;
    LCD_ShowString(30, 100, 150, 24, 24, (u8*)buffer);
    
    // 绘制时间设置卡片
    DrawCard(20, 140, 200, 60, MODERN_CARD_COLOR,
             (g_ModernSelectedItem == 2) ? MODERN_ACCENT_COLOR : MODERN_LIGHT_COLOR);
    
    POINT_COLOR = MODERN_TEXT_COLOR;
    BACK_COLOR = MODERN_CARD_COLOR;
    LCD_ShowString(30, 150, 100, 16, 16, (u8*)"Time:");
    
    sprintf(buffer, "%02d:%02d:%02d", time->hour, time->minute, time->second);
    POINT_COLOR = MODERN_ACCENT_COLOR;
    LCD_ShowString(30, 170, 150, 24, 24, (u8*)buffer);
    
    // 绘制星期显示
    POINT_COLOR = MODERN_TEXT_COLOR;
    BACK_COLOR = MODERN_BG_COLOR;
    sprintf(buffer, "Weekday: %s", GetWeekdayName(time->week));
    LCD_ShowString(30, 210, 200, 16, 16, (u8*)buffer);
    
    // 绘制操作按钮
    Clock_DrawModernButton(20, 250, 60, 30, "SAVE", 
                          (g_ModernSelectedItem == 3));
    Clock_DrawModernButton(90, 250, 60, 30, "CANCEL", 
                          (g_ModernSelectedItem == 0));
    Clock_DrawModernButton(160, 250, 60, 30, "SYNC", 
                          (g_ModernSelectedItem == 4));
    
    g_ModernNeedRefresh = 0;
}

// 现代简约风格按键处理
void Clock_ProcessModernKey(uint8_t key)
{
    switch (key) {
        case KEY1_PRESSED:  // 确认/进入
            switch (g_ModernCurrentPage) {
                case PAGE_CLOCK:
                    if (g_ModernSelectedItem == 0) {
                        g_ModernCurrentPage = PAGE_ALARM_LIST;
                        g_ModernSelectedItem = 0;
                        g_ModernNeedRefresh = 1;
                    } else if (g_ModernSelectedItem == 1) {
                        g_ModernCurrentPage = PAGE_TIME_SET;
                        g_ModernSelectedItem = 0;
                        g_ModernNeedRefresh = 1;
                    } else if (g_ModernSelectedItem == 2) {
                        // 切换样式
                        Clock_SetStyle(STYLE_CLASSIC);
                        g_ModernNeedRefresh = 1;
                    }
                    break;
                    
                case PAGE_ALARM_LIST:
                    if (g_ModernSelectedItem == 0) {
                        g_ModernCurrentPage = PAGE_CLOCK;
                        g_ModernSelectedItem = 0;
                        g_ModernNeedRefresh = 1;
                    } else if (g_ModernSelectedItem == g_AlarmManager.count + 1) {
                        g_ModernCurrentPage = PAGE_ALARM_EDIT;
                        g_ModernSelectedItem = 0;
                        g_ModernNeedRefresh = 1;
                    }
                    break;
                    
                case PAGE_ALARM_EDIT:
                case PAGE_TIME_SET:
                    if (g_ModernSelectedItem == 0) {
                        g_ModernCurrentPage = PAGE_ALARM_LIST;
                        g_ModernSelectedItem = 0;
                        g_ModernNeedRefresh = 1;
                    }
                    break;
            }
            break;
            
        case KEY2_PRESSED:  // 上一项
            if (g_ModernSelectedItem > 0) {
                g_ModernSelectedItem--;
            }
            g_ModernNeedRefresh = 1;
            break;
            
        case KEY3_PRESSED:  // 下一项
            uint8_t max_items = 3;  // 默认最大项目数
            switch (g_ModernCurrentPage) {
                case PAGE_ALARM_LIST:
                    max_items = g_AlarmManager.count + 1;
                    break;
                case PAGE_ALARM_EDIT:
                    max_items = 5;
                    break;
                case PAGE_TIME_SET:
                    max_items = 4;
                    break;
            }
            
            if (g_ModernSelectedItem < max_items) {
                g_ModernSelectedItem++;
            }
            g_ModernNeedRefresh = 1;
            break;
    }
}
