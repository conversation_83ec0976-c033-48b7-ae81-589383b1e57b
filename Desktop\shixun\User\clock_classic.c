#include "clock_styles.h"
#include "clock_display.h"
#include "key.h"
#include "uart_protocol.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

// 经典复古风格的全局变量
static uint8_t g_ClassicCurrentPage = PAGE_CLOCK;
static uint8_t g_ClassicSelectedItem = 0;
static uint8_t g_ClassicNeedRefresh = 1;
static DateTime g_ClassicLastTime = {0};

// 经典复古风格模拟时钟
void Clock_DrawClassicAnalogClock(DateTime* time)
{
    static uint8_t first_draw = 1;
    
    if (first_draw || g_ClassicNeedRefresh) {
        // 绘制时钟背景
        LCD_Clear(CLASSIC_BG_COLOR);
        
        // 绘制外圈装饰
        POINT_COLOR = CLASSIC_WOOD_COLOR;
        for (int i = 0; i < 3; i++) {
            LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CLOCK_RADIUS + 5 + i);
        }
        
        // 绘制时钟面板
        LCD_Fill(CLOCK_CENTER_X - CLOCK_RADIUS, CLOCK_CENTER_Y - CLOCK_RADIUS,
                 CLOCK_CENTER_X + CLOCK_RADIUS, CLOCK_CENTER_Y + CLOCK_RADIUS, 
                 CLASSIC_LIGHT_COLOR);
        
        POINT_COLOR = CLASSIC_WOOD_COLOR;
        LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CLOCK_RADIUS);
        
        // 绘制罗马数字
        Clock_DrawClassicNumbers();
        
        // 绘制刻度
        Clock_DrawClassicMarks();
        
        first_draw = 0;
        g_ClassicNeedRefresh = 0;
    }
    
    // 只在时间变化时重绘指针
    if (g_ClassicLastTime.hour != time->hour ||
        g_ClassicLastTime.minute != time->minute ||
        g_ClassicLastTime.second != time->second) {
        
        // 清除旧指针
        if (g_ClassicLastTime.year != 0) {
            Clock_DrawClassicHands(&g_ClassicLastTime, CLASSIC_LIGHT_COLOR);
        }
        
        // 绘制新指针
        Clock_DrawClassicHands(time, CLASSIC_TEXT_COLOR);
        
        // 绘制中心装饰
        POINT_COLOR = CLASSIC_ACCENT_COLOR;
        LCD_Fill(CLOCK_CENTER_X - 4, CLOCK_CENTER_Y - 4,
                 CLOCK_CENTER_X + 4, CLOCK_CENTER_Y + 4, CLASSIC_ACCENT_COLOR);
        LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, 4);
        
        g_ClassicLastTime = *time;
    }
}

// 绘制经典罗马数字
void Clock_DrawClassicNumbers(void)
{
    const char* roman_nums[] = {"XII", "I", "II", "III", "IV", "V", 
                               "VI", "VII", "VIII", "IX", "X", "XI"};
    
    POINT_COLOR = CLASSIC_TEXT_COLOR;
    BACK_COLOR = CLASSIC_LIGHT_COLOR;
    
    for (int i = 0; i < 12; i++) {
        float angle = i * 30 * M_PI / 180;
        int x = CLOCK_CENTER_X + (CLOCK_RADIUS - 15) * sin(angle);
        int y = CLOCK_CENTER_Y - (CLOCK_RADIUS - 15) * cos(angle);
        
        // 调整文字位置
        x -= strlen(roman_nums[i]) * 4;
        y -= 8;
        
        LCD_ShowString(x, y, 30, 16, 16, (u8*)roman_nums[i]);
    }
}

// 绘制经典刻度
void Clock_DrawClassicMarks(void)
{
    POINT_COLOR = CLASSIC_TEXT_COLOR;
    
    // 绘制小时刻度
    for (int i = 0; i < 12; i++) {
        float angle = i * 30 * M_PI / 180;
        int x1 = CLOCK_CENTER_X + (CLOCK_RADIUS - 5) * sin(angle);
        int y1 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 5) * cos(angle);
        int x2 = CLOCK_CENTER_X + (CLOCK_RADIUS - 12) * sin(angle);
        int y2 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 12) * cos(angle);
        
        // 绘制粗刻度线
        for (int j = -1; j <= 1; j++) {
            LCD_DrawLine(x1 + j, y1, x2 + j, y2);
            LCD_DrawLine(x1, y1 + j, x2, y2 + j);
        }
    }
    
    // 绘制分钟刻度
    POINT_COLOR = CLASSIC_LIGHT_COLOR;
    for (int i = 0; i < 60; i++) {
        if (i % 5 != 0) {  // 跳过小时刻度位置
            float angle = i * 6 * M_PI / 180;
            int x1 = CLOCK_CENTER_X + (CLOCK_RADIUS - 3) * sin(angle);
            int y1 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 3) * cos(angle);
            int x2 = CLOCK_CENTER_X + (CLOCK_RADIUS - 8) * sin(angle);
            int y2 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 8) * cos(angle);
            
            LCD_DrawLine(x1, y1, x2, y2);
        }
    }
}

// 绘制经典指针
void Clock_DrawClassicHands(DateTime* time, uint16_t color)
{
    // 计算指针角度
    float hour_angle = ((time->hour % 12) * 30 + time->minute * 0.5) * M_PI / 180;
    float minute_angle = time->minute * 6 * M_PI / 180;
    float second_angle = time->second * 6 * M_PI / 180;
    
    POINT_COLOR = color;
    
    // 绘制时针（短而粗）
    int hour_x = CLOCK_CENTER_X + (CLOCK_RADIUS - 25) * sin(hour_angle);
    int hour_y = CLOCK_CENTER_Y - (CLOCK_RADIUS - 25) * cos(hour_angle);
    for (int i = -2; i <= 2; i++) {
        for (int j = -2; j <= 2; j++) {
            LCD_DrawLine(CLOCK_CENTER_X + i, CLOCK_CENTER_Y + j, 
                        hour_x + i, hour_y + j);
        }
    }
    
    // 绘制分针（中等长度）
    int minute_x = CLOCK_CENTER_X + (CLOCK_RADIUS - 15) * sin(minute_angle);
    int minute_y = CLOCK_CENTER_Y - (CLOCK_RADIUS - 15) * cos(minute_angle);
    for (int i = -1; i <= 1; i++) {
        for (int j = -1; j <= 1; j++) {
            LCD_DrawLine(CLOCK_CENTER_X + i, CLOCK_CENTER_Y + j, 
                        minute_x + i, minute_y + j);
        }
    }
    
    // 绘制秒针（细长）
    if (color != CLASSIC_LIGHT_COLOR) {  // 只在绘制新指针时显示秒针
        POINT_COLOR = CLASSIC_WARNING_COLOR;
        int second_x = CLOCK_CENTER_X + (CLOCK_RADIUS - 10) * sin(second_angle);
        int second_y = CLOCK_CENTER_Y - (CLOCK_RADIUS - 10) * cos(second_angle);
        LCD_DrawLine(CLOCK_CENTER_X, CLOCK_CENTER_Y, second_x, second_y);
    }
}

// 经典复古风格闹钟列表
void Clock_DrawClassicAlarmList(void)
{
    char buffer[50];
    
    // 清屏并绘制背景
    LCD_Clear(CLASSIC_BG_COLOR);
    Clock_DrawClassicWoodFrame(10, 10, 220, 300);
    
    // 绘制标题
    POINT_COLOR = CLASSIC_ACCENT_COLOR;
    BACK_COLOR = CLASSIC_BG_COLOR;
    LCD_ShowString(80, 30, 200, 24, 24, (u8*)"ALARMS");
    
    // 绘制闹钟列表
    for (uint8_t i = 0; i < g_AlarmManager.count && i < 5; i++) {
        Alarm* alarm = &g_AlarmManager.alarms[i];
        uint8_t y_pos = 60 + i * 35;
        uint8_t selected = (g_ClassicSelectedItem == i + 1);
        
        // 绘制选中背景
        if (selected) {
            LCD_Fill(20, y_pos - 2, 220, y_pos + 30, CLASSIC_ACCENT_COLOR);
            BACK_COLOR = CLASSIC_ACCENT_COLOR;
        } else {
            BACK_COLOR = CLASSIC_BG_COLOR;
        }
        
        // 绘制闹钟时间
        POINT_COLOR = alarm->enabled ? CLASSIC_TEXT_COLOR : CLASSIC_LIGHT_COLOR;
        sprintf(buffer, "%02d:%02d", alarm->hour, alarm->minute);
        LCD_ShowString(30, y_pos, 100, 16, 16, (u8*)buffer);
        
        // 绘制闹钟名称
        LCD_ShowString(30, y_pos + 15, 120, 16, 12, (u8*)alarm->name);
        
        // 绘制状态
        POINT_COLOR = alarm->enabled ? CLASSIC_SUCCESS_COLOR : CLASSIC_WARNING_COLOR;
        LCD_ShowString(170, y_pos + 5, 40, 16, 16, 
                      (u8*)(alarm->enabled ? "ON" : "OFF"));
    }
    
    // 绘制操作按钮
    Clock_DrawClassicButton(30, 250, 50, 25, "ADD", 
                           (g_ClassicSelectedItem == g_AlarmManager.count + 1));
    Clock_DrawClassicButton(90, 250, 50, 25, "EDIT", 
                           (g_ClassicSelectedItem == g_AlarmManager.count + 2));
    Clock_DrawClassicButton(150, 250, 50, 25, "BACK", 
                           (g_ClassicSelectedItem == 0));
    
    g_ClassicNeedRefresh = 0;
}

// 经典复古风格闹钟编辑
void Clock_DrawClassicAlarmEdit(uint8_t index)
{
    char buffer[50];
    
    // 清屏并绘制背景
    LCD_Clear(CLASSIC_BG_COLOR);
    Clock_DrawClassicWoodFrame(10, 10, 220, 300);
    
    // 绘制标题
    POINT_COLOR = CLASSIC_ACCENT_COLOR;
    BACK_COLOR = CLASSIC_BG_COLOR;
    const char* title = (index < g_AlarmManager.count) ? "EDIT ALARM" : "NEW ALARM";
    LCD_ShowString(60, 30, 200, 24, 24, (u8*)title);
    
    // 获取当前编辑的闹钟
    Alarm* current_alarm = (index < g_AlarmManager.count) ? 
                          &g_AlarmManager.alarms[index] : NULL;
    
    // 绘制时间设置区域
    POINT_COLOR = CLASSIC_TEXT_COLOR;
    BACK_COLOR = CLASSIC_BG_COLOR;
    LCD_ShowString(30, 70, 100, 16, 16, (u8*)"Time:");
    
    if (g_ClassicSelectedItem == 1) {
        LCD_Fill(30, 90, 150, 115, CLASSIC_ACCENT_COLOR);
        BACK_COLOR = CLASSIC_ACCENT_COLOR;
    }
    
    if (current_alarm) {
        sprintf(buffer, "%02d:%02d:%02d", 
                current_alarm->hour, current_alarm->minute, current_alarm->second);
    } else {
        sprintf(buffer, "00:00:00");
    }
    LCD_ShowString(30, 95, 120, 20, 20, (u8*)buffer);
    
    // 绘制重复设置
    POINT_COLOR = CLASSIC_TEXT_COLOR;
    BACK_COLOR = CLASSIC_BG_COLOR;
    LCD_ShowString(30, 130, 100, 16, 16, (u8*)"Repeat:");
    
    if (g_ClassicSelectedItem == 2) {
        LCD_Fill(30, 150, 180, 170, CLASSIC_ACCENT_COLOR);
        BACK_COLOR = CLASSIC_ACCENT_COLOR;
    }
    
    if (current_alarm) {
        if (current_alarm->days == 0x7F) {
            strcpy(buffer, "Every day");
        } else if (current_alarm->days == 0x1F) {
            strcpy(buffer, "Weekdays");
        } else {
            strcpy(buffer, "Custom");
        }
    } else {
        strcpy(buffer, "Every day");
    }
    LCD_ShowString(30, 155, 150, 16, 16, (u8*)buffer);
    
    // 绘制名称设置
    POINT_COLOR = CLASSIC_TEXT_COLOR;
    BACK_COLOR = CLASSIC_BG_COLOR;
    LCD_ShowString(30, 190, 100, 16, 16, (u8*)"Name:");
    
    if (g_ClassicSelectedItem == 3) {
        LCD_Fill(30, 210, 180, 230, CLASSIC_ACCENT_COLOR);
        BACK_COLOR = CLASSIC_ACCENT_COLOR;
    }
    
    if (current_alarm) {
        LCD_ShowString(30, 215, 150, 16, 16, (u8*)current_alarm->name);
    } else {
        LCD_ShowString(30, 215, 150, 16, 16, (u8*)"New Alarm");
    }
    
    // 绘制操作按钮
    Clock_DrawClassicButton(30, 260, 50, 25, "SAVE", 
                           (g_ClassicSelectedItem == 4));
    Clock_DrawClassicButton(90, 260, 50, 25, "CANCEL", 
                           (g_ClassicSelectedItem == 0));
    Clock_DrawClassicButton(150, 260, 50, 25, "DELETE", 
                           (g_ClassicSelectedItem == 5));
    
    g_ClassicNeedRefresh = 0;
}

// 经典复古风格时间设置
void Clock_DrawClassicTimeSet(DateTime* time)
{
    char buffer[50];
    
    // 清屏并绘制背景
    LCD_Clear(CLASSIC_BG_COLOR);
    Clock_DrawClassicWoodFrame(10, 10, 220, 300);
    
    // 绘制标题
    POINT_COLOR = CLASSIC_ACCENT_COLOR;
    BACK_COLOR = CLASSIC_BG_COLOR;
    LCD_ShowString(60, 30, 200, 24, 24, (u8*)"SET TIME");
    
    // 绘制日期设置
    POINT_COLOR = CLASSIC_TEXT_COLOR;
    LCD_ShowString(30, 70, 100, 16, 16, (u8*)"Date:");
    
    if (g_ClassicSelectedItem == 1) {
        LCD_Fill(30, 90, 180, 115, CLASSIC_ACCENT_COLOR);
        BACK_COLOR = CLASSIC_ACCENT_COLOR;
    } else {
        BACK_COLOR = CLASSIC_BG_COLOR;
    }
    
    sprintf(buffer, "%04d-%02d-%02d", time->year, time->month, time->day);
    LCD_ShowString(30, 95, 150, 20, 20, (u8*)buffer);
    
    // 绘制时间设置
    POINT_COLOR = CLASSIC_TEXT_COLOR;
    BACK_COLOR = CLASSIC_BG_COLOR;
    LCD_ShowString(30, 130, 100, 16, 16, (u8*)"Time:");
    
    if (g_ClassicSelectedItem == 2) {
        LCD_Fill(30, 150, 150, 175, CLASSIC_ACCENT_COLOR);
        BACK_COLOR = CLASSIC_ACCENT_COLOR;
    } else {
        BACK_COLOR = CLASSIC_BG_COLOR;
    }
    
    sprintf(buffer, "%02d:%02d:%02d", time->hour, time->minute, time->second);
    LCD_ShowString(30, 155, 120, 20, 20, (u8*)buffer);
    
    // 绘制星期显示
    POINT_COLOR = CLASSIC_TEXT_COLOR;
    BACK_COLOR = CLASSIC_BG_COLOR;
    sprintf(buffer, "Weekday: %s", GetWeekdayName(time->week));
    LCD_ShowString(30, 190, 180, 16, 16, (u8*)buffer);
    
    // 绘制操作按钮
    Clock_DrawClassicButton(30, 260, 50, 25, "SAVE", 
                           (g_ClassicSelectedItem == 3));
    Clock_DrawClassicButton(90, 260, 50, 25, "CANCEL", 
                           (g_ClassicSelectedItem == 0));
    Clock_DrawClassicButton(150, 260, 50, 25, "SYNC", 
                           (g_ClassicSelectedItem == 4));
    
    g_ClassicNeedRefresh = 0;
}
