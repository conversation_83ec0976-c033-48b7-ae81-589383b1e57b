#ifndef __LED_H
#define __LED_H

#include "stm32f10x.h"

// LED pin definitions
#define LED1_PIN     GPIO_Pin_8
#define LED1_PORT    GPIOA
#define LED1_CLK     RCC_APB2Periph_GPIOA

#define LED2_PIN     GPIO_Pin_2
#define LED2_PORT    GPIOD
#define LED2_CLK     RCC_APB2Periph_GPIOD

// Function declarations
void LED_Init(void);
void LED1_On(void);
void LED1_Off(void);
void LED1_Toggle(void);
void LED2_On(void);
void LED2_Off(void);
void LED2_Toggle(void);
void LED_Toggle(void);
void LED_StartBlink(void);
void LED_StopBlink(void);

#endif /* __LED_H */ 