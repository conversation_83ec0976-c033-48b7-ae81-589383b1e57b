/**
  ******************************************************************************
  * @file    Project/STM32F10x_StdPeriph_Template/stm32f10x_it.c 
  * <AUTHOR> Application Team
  * @version V3.5.0
  * @date    08-April-2011
  * @brief   Main Interrupt Service Routines.
  *          This file provides template for all exceptions handler and 
  *          peripherals interrupt service routine.
  ******************************************************************************
  * @attention
  *
  * THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
  * WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO SAVE
  * TIME. AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR ANY
  * DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY CLAIMS ARISING
  * FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOMERS OF THE
  * CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR PRODUCTS.
  *
  * <h2><center>&copy; COPYRIGHT 2011 STMicroelectronics</center></h2>
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stm32f10x_it.h"
#include "rtc.h"
#include "led.h"

/** @addtogroup STM32F10x_StdPeriph_Template
  * @{
  */

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/******************************************************************************/
/*            Cortex-M3 Processor Exceptions Handlers                         */
/******************************************************************************/

/**
  * @brief  This function handles NMI exception.
  * @param  None
  * @retval None
  */
void NMI_Handler(void)
{
}

/**
  * @brief  This function handles Hard Fault exception.
  * @param  None
  * @retval None
  */
void HardFault_Handler(void)
{
  /* Go to infinite loop when Hard Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Memory Manage exception.
  * @param  None
  * @retval None
  */
void MemManage_Handler(void)
{
  /* Go to infinite loop when Memory Manage exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Bus Fault exception.
  * @param  None
  * @retval None
  */
void BusFault_Handler(void)
{
  /* Go to infinite loop when Bus Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Usage Fault exception.
  * @param  None
  * @retval None
  */
void UsageFault_Handler(void)
{
  /* Go to infinite loop when Usage Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles SVCall exception.
  * @param  None
  * @retval None
  */
void SVC_Handler(void)
{
}

/**
  * @brief  This function handles Debug Monitor exception.
  * @param  None
  * @retval None
  */
void DebugMon_Handler(void)
{
}

/**
  * @brief  This function handles PendSVC exception.
  * @param  None
  * @retval None
  */
void PendSV_Handler(void)
{
}

/**
  * @brief  This function handles SysTick Handler.
  * @param  None
  * @retval None
  */
//void SysTick_Handler(void)
//{
//}

/******************************************************************************/
/*                 STM32F10x Peripherals Interrupt Handlers                   */
/*  Add here the Interrupt Handler for the used peripheral(s) (PPP), for the  */
/*  available peripheral interrupt handler's name please refer to the startup */
/*  file (startup_stm32f10x_xx.s).                                            */
/******************************************************************************/

/**
  * @brief  This function handles PPP interrupt request.
  * @param  None
  * @retval None
  */
/*void PPP_IRQHandler(void)
{
}*/

/**
  * @brief  This function handles TIM2 interrupt request.
  * @param  None
  * @retval None
  */
void TIM2_IRQHandler(void)
{
    if (TIM_GetITStatus(TIM2, TIM_IT_Update) != RESET)
    {
        // 清除中断标志
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);

        // 更新毫秒计数器
        g_TimerCounter++;

        // 更新毫秒
        g_DateTime.millisecond += 1;
        if (g_DateTime.millisecond >= 1000)
        {
            g_DateTime.millisecond = 0;
            
            // 更新秒数
            g_DateTime.second++;
            if (g_DateTime.second >= 60)
            {
                g_DateTime.second = 0;
                
                // 更新分钟
                g_DateTime.minute++;
                if (g_DateTime.minute >= 60)
                {
                    g_DateTime.minute = 0;
                    
                    // 更新小时
                    g_DateTime.hour++;
                    if (g_DateTime.hour >= 24)
                    {
                        g_DateTime.hour = 0;
                        
                        // 更新星期
                        g_DateTime.week++;
                        if (g_DateTime.week >= 7)
                            g_DateTime.week = 0;
                            
                        // 更新日期
                        g_DateTime.day++;
                        uint8_t monthDays = g_MonthDays[g_DateTime.month - 1];
                        
                        // 处理闰年2月
                        if (g_DateTime.month == 2 && IsLeapYear(g_DateTime.year))
                            monthDays = 29;
                        
                        if (g_DateTime.day > monthDays)
                        {
                            g_DateTime.day = 1;
                            
                            // 更新月份
                            g_DateTime.month++;
                            if (g_DateTime.month > 12)
                            {
                                g_DateTime.month = 1;
                                
                                // 更新年份
                                g_DateTime.year++;
                            }
                        }
                    }
                }
            }

            // 不再定期发送时间信息到串口
            // 只有在闹钟触发或串口命令请求时才发送

            // 闹钟检查 (LED闪烁和串口发送在Alarm_Check函数内部处理)
            Alarm_Check(&g_AlarmManager, &g_DateTime);
        }
    }
}

/**
  * @brief  This function handles TIM3 interrupt request.
  * @param  None
  * @retval None
  */
void TIM3_IRQHandler(void)
{
    if (TIM_GetITStatus(TIM3, TIM_IT_Update) != RESET)
    {
        // 清除中断标志
        TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
        
        // 切换LED状态
        LED1_Toggle();
        LED2_Toggle();
        
        // 闪烁200次后停止
        static uint8_t blink_count = 0;
        blink_count++;
        
        // 每10次打印一次闪烁进度
        if(blink_count % 10 == 0) {
            printf("LED闪烁: %d/200\r\n", blink_count);
        }
        
        if (blink_count >= 200) // 闪烁200次后停止
        {
            blink_count = 0;
            printf("LED闪烁结束\r\n");
            LED_StopBlink();
        }
    }
}

/**
  * @}
  */ 


/******************* (C) COPYRIGHT 2011 STMicroelectronics *****END OF FILE****/
