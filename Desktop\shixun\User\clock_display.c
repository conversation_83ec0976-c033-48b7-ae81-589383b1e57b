#include "clock_display.h"
#include "key.h"
#include "uart_protocol.h"
#include <math.h>
#include <stdio.h>
#include <string.h>

// 数学常量
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// 星期名称字符串数组
static const char* weekday_names[] = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};
static const char* month_names[] = {"", "January", "February", "March", "April", "May", "June",
                                   "July", "August", "September", "October", "November", "December"};

// 上一次绘制的时间，用于清除指针
static DateTime g_LastDrawTime = {0};

// 自定义LCD_DrawPoint_big函数实现
void LCD_DrawPoint_big(u16 x,u16 y)
{
    LCD_Fill(x-1, y-1, x+1, y+1, POINT_COLOR);
}

// 全局变量
static uint8_t g_CurrentPage = PAGE_CLOCK;      // 当前页面
static uint8_t g_CurrentAlarmIndex = 0;         // 当前编辑的闹钟索引
static uint8_t g_NeedRefresh = 1;               // 是否需要刷新显示
static uint8_t g_LastSecond = 0;                // 上次更新的秒数
static uint8_t g_LastMinute = 0;                // 上次更新的分钟
static uint8_t g_LastHour = 0;                  // 上次更新的小时
static uint8_t g_LastDay = 0;                   // 上次更新的日期
static uint8_t g_LastMonth = 0;                 // 上次更新的月份
static uint16_t g_LastYear = 0;                 // 上次更新的年份
static uint8_t g_LastWeek = 0;                  // 上次更新的星期
static uint8_t g_LastUpdateTime = 0;            // 上次更新的时间（秒）
static uint8_t g_SelectedItem = 0;              // 当前选中的项目
static uint8_t g_EditingField = 0;              // 当前编辑的字段（闹钟编辑时）
static uint8_t g_TimeEditMode = 0;              // 时间编辑模式：0-无，1-小时，2-分钟，3-秒
static Alarm g_TempAlarm;                       // 临时闹钟存储
static uint8_t g_FirstTimeEnter = 1;            // 标记是否首次进入编辑页面
static DateTime g_TempDateTime;                 // 临时日期时间存储
static uint8_t g_ForceRefreshDate = 0;          // 强制刷新日期信息标志

// 选择回调函数声明
static void SelectCallback_GotoAlarmList(void);
static void SelectCallback_GotoClock(void);
static void SelectCallback_AddAlarm(void);
static void SelectCallback_EditAlarm(uint8_t index);
static void SelectCallback_DeleteAlarm(uint8_t index);
static void SelectCallback_SaveAlarm(void);
static void SelectCallback_CancelEdit(void);
static void SelectCallback_SetTime(void);
static void SelectCallback_SaveTime(void);
static void SelectCallback_CancelTimeSet(void);

// 时间刷新函数声明
void Clock_RefreshTimeDigit(DateTime* time);

// 初始化时钟显示
void Clock_Init(void)
{
    // 初始化LCD
    LCD_Init();
    LCD_Clear(WHITE);

    // 设置默认页面
    g_CurrentPage = PAGE_CLOCK;
    g_NeedRefresh = 1;
}

// 切换页面
void Clock_ChangePage(uint8_t page)
{
    // 清空屏幕
    LCD_Clear(WHITE);

    // 设置页面
    g_CurrentPage = page;

    // 重置选择项
    g_SelectedItem = 0;

    // 从闹钟编辑页面切换出去，确保清除编辑状态
    if (page != PAGE_ALARM_EDIT) {
        g_EditingField = 0;
        g_TimeEditMode = 0;
    }

    // 设置刷新标志
    g_NeedRefresh = 1;

    // 强制刷新日期信息，确保切换页面后日期信息显示
    Clock_ForceRefreshDateInfo();
}

// 显示数字时钟页面
void Clock_DrawDigitalTime(DateTime* time)
{
    char buffer[30];
    static const char* weekdays[] = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};
    
    // 检查是否需要全部刷新
    if (g_NeedRefresh) {
        // 清空屏幕显示，使用黑色背景
        LCD_Clear(BLACK);

        // 显示标题
        POINT_COLOR = RED;
        BACK_COLOR = BLACK;
        LCD_ShowString(85, 10, 200, 24, 24, (u8*)"CLOCK");
        
        // 显示初始时间框架，使用固定的坐标
        // 小时: 60-84   冒号1: 84-96   分钟: 96-120   冒号2: 120-132   秒: 132-156
        LCD_ShowString(60, 110, 40, 24, 24, (u8*)"00");
        LCD_ShowString(84, 110, 12, 24, 24, (u8*)":");
        LCD_ShowString(96, 110, 40, 24, 24, (u8*)"00");
        LCD_ShowString(120, 110, 12, 24, 24, (u8*)":");
        LCD_ShowString(132, 110, 40, 24, 24, (u8*)"00");
        
        // 绘制底部菜单栏

        // 闹钟列表按钮
        if (g_SelectedItem == 0) {
            POINT_COLOR = RED;
            LCD_Fill(20, 200, 110, 230, GRAY); // 选中背景为灰色
            BACK_COLOR = GRAY;
        } else {
            POINT_COLOR = GRAY;
            LCD_Fill(20, 200, 110, 230, BLACK); // 普通背景为黑色
            BACK_COLOR = BLACK;
        }
        LCD_DrawRectangle(20, 200, 110, 230);
        LCD_ShowString(30, 208, 200, 16, 16, (u8*)"ALARM LIST");
        
        // 时间设置按钮
        if (g_SelectedItem == 1) {
    POINT_COLOR = RED;
            LCD_Fill(130, 200, 220, 230, GRAY); // 选中背景为灰色
            BACK_COLOR = GRAY;
        } else {
            POINT_COLOR = GRAY;
            LCD_Fill(130, 200, 220, 230, BLACK); // 普通背景为黑色
            BACK_COLOR = BLACK;
        }
        LCD_DrawRectangle(130, 200, 220, 230);
        LCD_ShowString(140, 208, 200, 16, 16, (u8*)"SET TIME");
        
        // �ָ�Ĭ�ϱ���ɫ
        BACK_COLOR = BLACK;
        
        // ǿ��ȫ���ػ�
        g_LastSecond = 0xFF;
        g_LastMinute = 0xFF;
        g_LastHour = 0xFF;
        g_LastDay = 0xFF;
        g_LastMonth = 0xFF;
        g_LastYear = 0xFFFF;
        g_LastWeek = 0xFF;
        
        g_NeedRefresh = 0;
    }
    
    // ֻ���±仯�Ĳ���
    if (g_LastUpdateTime != time->second || g_LastHour != time->hour || 
        g_LastMinute != time->minute || g_LastSecond != time->second ||
        g_LastDay != time->day || g_LastMonth != time->month || 
        g_LastYear != time->year || g_LastWeek != time->week) {
        
        Clock_RefreshTimeDigit(time);
        g_LastUpdateTime = time->second;
    }
}

// ����ʱ�ӱ���
void Clock_DrawDial(void)
{
    int i;
    int x, y;
    float angle;
    
    // ���Ʊ�����Ȧ
    POINT_COLOR = BLACK;
    LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CLOCK_RADIUS);
    
    // ���ƿ̶�
    for(i = 0; i < 12; i++) {
        angle = i * 30 * 3.14159 / 180;
        x = CLOCK_CENTER_X + (CLOCK_RADIUS - 10) * sin(angle);
        y = CLOCK_CENTER_Y - (CLOCK_RADIUS - 10) * cos(angle);
        
        // ����Сʱ�̶�
        LCD_DrawPoint_big(x, y);
    }
}



// ��ʾ����
void Clock_DrawDate(DateTime* time)
{
    // �˺������ٵ���ʹ�ã������Ѻϲ���Clock_DrawDigitalTime��
    // ����������ά�ּ�����
    Clock_DrawDigitalTime(time);
}

// ���������б�ҳ��
void Clock_DrawAlarmList(void)
{
    uint8_t i;
    char buffer[30];
    static const char* weekdays[] = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
    
    // ������ʹ�ø��ֵ�����
    LCD_Clear(LIGHTBLUE);
    BACK_COLOR = LIGHTBLUE;

    // ��ʾ���� - ���ӱ�����Ӱ
    POINT_COLOR = DARKBLUE;
    LCD_Fill(5, 5, 235, 35, WHITE);
    LCD_DrawRectangle(5, 5, 235, 35);
    LCD_DrawRectangle(6, 6, 234, 34);
    BACK_COLOR = WHITE;
    LCD_ShowString(70, 12, 200, 24, 24, (u8*)"ALARM LIST");
    
    // ��ʾ�����б�
    for(i = 0; i < g_AlarmManager.count && i < 5; i++) {
        Alarm* alarm = &g_AlarmManager.alarms[i];
        
        // �����������
        if (g_SelectedItem == i + 1) {
            // ѡ���������ʾ
            POINT_COLOR = RED;
            LCD_Fill(10, 50 + i * 40, 230, 80 + i * 40, GRAY);
            BACK_COLOR = GRAY;
        } else if(alarm->enabled) {
            POINT_COLOR = RED;
            LCD_DrawRectangle(10, 50 + i * 40, 230, 80 + i * 40);
            BACK_COLOR = BLACK;
        } else {
            POINT_COLOR = GRAY;
            LCD_DrawRectangle(10, 50 + i * 40, 230, 80 + i * 40);
            BACK_COLOR = BLACK;
        }
        
        // ��ʾ����ʱ��
        sprintf(buffer, "%02d:%02d:%02d", alarm->hour, alarm->minute, alarm->second);
        LCD_ShowString(20, 55 + i * 40, 200, 16, 16, (u8*)buffer);
        
        // ��ʾ��������
        LCD_ShowString(120, 55 + i * 40, 200, 16, 16, (u8*)alarm->name);
        
        // ��ʾ�ظ�����
        POINT_COLOR = (g_SelectedItem == i + 1) ? RED : GRAY;
        buffer[0] = 0;
        for(uint8_t j = 0; j < 7; j++) {
            if(alarm->days & (1 << j)) {
                strcat(buffer, weekdays[j]);
                strcat(buffer, " ");
            }
        }
        LCD_ShowString(20, 75 + i * 40, 200, 16, 12, (u8*)buffer);
    }
    
    // ��ʾ���Ӱ�ť
    if (g_SelectedItem == g_AlarmManager.count + 1) {
        // ѡ���������ʾ
        POINT_COLOR = RED;
        LCD_Fill(10, 50 + i * 40, 230, 80 + i * 40, GRAY);
        BACK_COLOR = GRAY;
    } else {
    POINT_COLOR = RED;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(10, 50 + i * 40, 230, 80 + i * 40);
    LCD_ShowString(100, 55 + i * 40, 200, 16, 16, (u8*)"+ Add");
    
    // ���Ʒ��ذ�ť
    if (g_SelectedItem == 0) {
        // ѡ���������ʾ
        POINT_COLOR = RED;
        LCD_Fill(10, 200, 230, 230, GRAY);
        BACK_COLOR = GRAY;
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(10, 200, 230, 230);
    LCD_ShowString(90, 208, 200, 16, 16, (u8*)"BACK");
    
    // �ָ�Ĭ�ϱ���ɫ
    BACK_COLOR = BLACK;
    g_NeedRefresh = 0;
}

// �������ӱ༭ҳ��
void Clock_DrawAlarmEdit(uint8_t index)
{
    char buffer[30];
    
    // ������ʹ�ú�ɫ����
    LCD_Clear(BLACK);
    BACK_COLOR = BLACK;
    
    // ��ʾ����
    POINT_COLOR = RED;
    
    if(index < g_AlarmManager.count) {
        LCD_ShowString(10, 10, 200, 24, 24, (u8*)"Edit Alarm");
        
        if(g_FirstTimeEnter) {
            // ���Ƶ���ʱ����
            memcpy(&g_TempAlarm, &g_AlarmManager.alarms[index], sizeof(Alarm));
            printf("Loading existing alarm: %02d:%02d:%02d, Days=%02X\r\n", 
                g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second, g_TempAlarm.days);
            g_FirstTimeEnter = 0;
        }
    } else {
        LCD_ShowString(10, 10, 200, 24, 24, (u8*)"New Alarm");
        
        if(g_FirstTimeEnter) {
            // ��ʼ��������
            memset(&g_TempAlarm, 0, sizeof(Alarm));
            sprintf(g_TempAlarm.name, "Alarm %d", g_AlarmManager.count);
            g_TempAlarm.enabled = 1;
            g_TempAlarm.days = 0x7F; // ÿ��
            printf("Created new alarm template\r\n");
            g_FirstTimeEnter = 0;
        }
    }
    
    // ���Դ�ӡ��ǰ������Ϣ
    printf("Current alarm editing: Time=%02d:%02d:%02d, Status=%d\r\n", 
        g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second, g_TempAlarm.enabled);
    
    // ��ʾʱ������ (g_EditingField == 1)
    POINT_COLOR = BLUE;
    LCD_ShowString(20, 50, 200, 16, 16, (u8*)"Time:");
    
    if (g_SelectedItem == 1) {
        if (g_EditingField == 1) {
            // ���ڱ༭ʱ��
            if (g_TimeEditMode == 1) {
                // �༭Сʱ��������ʾСʱ����
                POINT_COLOR = RED;
                BACK_COLOR = GRAY;
                sprintf(buffer, "%02d", g_TempAlarm.hour);
                LCD_Fill(100, 50, 115, 66, GRAY);
                LCD_ShowString(100, 50, 15, 16, 16, (u8*)buffer);
                
                // ��ʾ���Ӻ��루��������
                POINT_COLOR = GRAY;
                BACK_COLOR = BLACK;
                sprintf(buffer, ":%02d:%02d", g_TempAlarm.minute, g_TempAlarm.second);
                LCD_ShowString(115, 50, 200, 16, 16, (u8*)buffer);
            } else if (g_TimeEditMode == 2) {
                // �༭���ӣ�������ʾ���Ӳ���
                POINT_COLOR = GRAY;
                BACK_COLOR = BLACK;
                sprintf(buffer, "%02d:", g_TempAlarm.hour);
                LCD_ShowString(100, 50, 200, 16, 16, (u8*)buffer);
                
                // ������ʾ����
                POINT_COLOR = RED;
                BACK_COLOR = GRAY;
                sprintf(buffer, "%02d", g_TempAlarm.minute);
                LCD_Fill(118, 50, 133, 66, GRAY);
                LCD_ShowString(118, 50, 15, 16, 16, (u8*)buffer);
                
                // ��ʾ���ӣ���������
                POINT_COLOR = GRAY;
                BACK_COLOR = BLACK;
                sprintf(buffer, ":%02d", g_TempAlarm.second);
                LCD_ShowString(133, 50, 200, 16, 16, (u8*)buffer);
            } else {
                // ���������δѡ�����༭��
                POINT_COLOR = RED;
                BACK_COLOR = GRAY;
                LCD_Fill(100, 50, 180, 66, GRAY);
                sprintf(buffer, "%02d:%02d:%02d", g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second);
                LCD_ShowString(100, 50, 200, 16, 16, (u8*)buffer);
            }
        } else {
            // ѡ�е�δ�༭
            POINT_COLOR = RED;
            BACK_COLOR = BLACK;
            LCD_DrawRectangle(100, 50, 180, 66);
            sprintf(buffer, "%02d:%02d:%02d", g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second);
            LCD_ShowString(100, 50, 200, 16, 16, (u8*)buffer);
        }
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "%02d:%02d:%02d", g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second);
    LCD_ShowString(100, 50, 200, 16, 16, (u8*)buffer);
    }
    
    // ��ʾ�ظ����� (g_EditingField == 2)
    POINT_COLOR = BLUE;
    LCD_ShowString(20, 80, 200, 16, 16, (u8*)"Repeat:");
    
    if (g_SelectedItem == 2) {
        if (g_EditingField == 2) {
            // ���ڱ༭�ظ�
            POINT_COLOR = RED;
            BACK_COLOR = GRAY;
            LCD_Fill(100, 80, 220, 96, GRAY);
        } else {
            // ѡ�е�δ�༭
            POINT_COLOR = RED;
            BACK_COLOR = BLACK;
            LCD_DrawRectangle(100, 80, 220, 96);
        }
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    
    buffer[0] = 0;
    if(g_TempAlarm.days == 0x7F) {
        sprintf(buffer, "Every day");
    } else if(g_TempAlarm.days == 0x1F) {
        sprintf(buffer, "Weekdays");
    } else if(g_TempAlarm.days == 0x60) {
        sprintf(buffer, "Weekend");
    } else {
        if(g_TempAlarm.days & 0x01) strcat(buffer, "Sun ");
        if(g_TempAlarm.days & 0x02) strcat(buffer, "Mon ");
        if(g_TempAlarm.days & 0x04) strcat(buffer, "Tue ");
        if(g_TempAlarm.days & 0x08) strcat(buffer, "Wed ");
        if(g_TempAlarm.days & 0x10) strcat(buffer, "Thu ");
        if(g_TempAlarm.days & 0x20) strcat(buffer, "Fri ");
        if(g_TempAlarm.days & 0x40) strcat(buffer, "Sat ");
    }
    LCD_ShowString(100, 80, 200, 16, 16, (u8*)buffer);
    
    // ��ʾ�������� (g_EditingField == 3)
    POINT_COLOR = BLUE;
    LCD_ShowString(20, 110, 200, 16, 16, (u8*)"Name:");
    
    if (g_SelectedItem == 3) {
        if (g_EditingField == 3) {
            // ���ڱ༭����
            POINT_COLOR = RED;
            BACK_COLOR = GRAY;
            LCD_Fill(100, 110, 220, 126, GRAY);
        } else {
            // ѡ�е�δ�༭
            POINT_COLOR = RED;
            BACK_COLOR = BLACK;
            LCD_DrawRectangle(100, 110, 220, 126);
        }
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    LCD_ShowString(100, 110, 200, 16, 16, (u8*)g_TempAlarm.name);
    
    // ��ʾ����״̬ (g_EditingField == 4)
    POINT_COLOR = BLUE;
    LCD_ShowString(20, 140, 200, 16, 16, (u8*)"Status:");
    
    if (g_SelectedItem == 4) {
        if (g_EditingField == 4) {
            // ���ڱ༭״̬
            POINT_COLOR = RED;
            BACK_COLOR = GRAY;
            LCD_Fill(100, 140, 170, 156, GRAY);
        } else {
            // ѡ�е�δ�༭
            POINT_COLOR = RED;
            BACK_COLOR = BLACK;
            LCD_DrawRectangle(100, 140, 170, 156);
        }
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    LCD_ShowString(100, 140, 200, 16, 16, (u8*)(g_TempAlarm.enabled ? "Enabled" : "Disabled"));
    
    // ��ʾ���水ť
    if (g_SelectedItem == 5) {
        // ѡ���������ʾ
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        LCD_Fill(20, 180, 110, 210, GRAY);
    } else {
        POINT_COLOR = RED;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(20, 180, 110, 210);
    LCD_ShowString(40, 188, 200, 16, 16, (u8*)"SAVE");
    
    // ��ʾȡ����ť
    if (g_SelectedItem == 0) {
        // ѡ���������ʾ
    POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        LCD_Fill(130, 180, 220, 210, GRAY);
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(130, 180, 220, 210);
    LCD_ShowString(150, 188, 200, 16, 16, (u8*)"CANCEL");
    
    // �ָ�Ĭ�ϱ���ɫ
    BACK_COLOR = BLACK;
    g_NeedRefresh = 0;
    g_CurrentAlarmIndex = index;
}

// ����ʱ������ҳ��
void Clock_DrawTimeSet(DateTime* time)
{
    char buffer[30];
    static const char* weekdays[] = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
    
    // ������ʹ�ú�ɫ����
    LCD_Clear(BLACK);
    BACK_COLOR = BLACK;
    
    // ��ʾ����
    POINT_COLOR = RED;
    LCD_ShowString(70, 20, 200, 24, 24, (u8*)"TIME SETTINGS");
    
    // ֻ���״ν���ҳ��ʱ��ʼ����ʱʱ��
    if (g_FirstTimeEnter) {
        g_TempDateTime = *time;
        g_FirstTimeEnter = 0;
        printf("Loading current time: %02d:%02d:%02d, Date: %04d-%02d-%02d\r\n", 
               g_TempDateTime.hour, g_TempDateTime.minute, g_TempDateTime.second,
               g_TempDateTime.year, g_TempDateTime.month, g_TempDateTime.day);
    }
    
    // ��ʾ������
    POINT_COLOR = BLUE;
    LCD_ShowString(40, 60, 200, 16, 16, (u8*)"Date:");
    
    // ���ݱ༭ģʽ��ʾ����
    if (g_TimeEditMode == 4) {
        // �༭���
        POINT_COLOR = RED;
        sprintf(buffer, "%04d", g_TempDateTime.year);
        LCD_Fill(100, 60, 132, 76, GRAY);
        BACK_COLOR = GRAY;
        LCD_ShowString(100, 60, 32, 16, 16, (u8*)buffer);
        
        // ��ʾ����
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "-%02d-%02d", g_TempDateTime.month, g_TempDateTime.day);
        LCD_ShowString(132, 60, 200, 16, 16, (u8*)buffer);
    } else if (g_TimeEditMode == 5) {
        // �༭�·�
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "%04d-", g_TempDateTime.year);
        LCD_ShowString(100, 60, 200, 16, 16, (u8*)buffer);
        
        // ������ʾ�·�
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        sprintf(buffer, "%02d", g_TempDateTime.month);
        LCD_Fill(133, 60, 148, 76, GRAY);
        LCD_ShowString(133, 60, 15, 16, 16, (u8*)buffer);
        
        // ��ʾ����
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "-%02d", g_TempDateTime.day);
        LCD_ShowString(148, 60, 200, 16, 16, (u8*)buffer);
    } else if (g_TimeEditMode == 6) {
        // �༭����
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "%04d-%02d-", g_TempDateTime.year, g_TempDateTime.month);
        LCD_ShowString(100, 60, 200, 16, 16, (u8*)buffer);
        
        // ������ʾ����
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        sprintf(buffer, "%02d", g_TempDateTime.day);
        LCD_Fill(151, 60, 166, 76, GRAY);
        LCD_ShowString(151, 60, 15, 16, 16, (u8*)buffer);
    } else {
        if (g_SelectedItem == 3) {
            // ѡ��״̬
            POINT_COLOR = RED;
            LCD_DrawRectangle(100, 60, 180, 76);
        } else {
            POINT_COLOR = GRAY;
        }
        BACK_COLOR = BLACK;
        sprintf(buffer, "%04d-%02d-%02d", g_TempDateTime.year, g_TempDateTime.month, g_TempDateTime.day);
        LCD_ShowString(100, 60, 200, 16, 16, (u8*)buffer);
    }
    
    // ��ʾ����
    POINT_COLOR = BLUE;
    BACK_COLOR = BLACK;
    sprintf(buffer, "Weekday: %s", weekdays[g_TempDateTime.week]);
    LCD_ShowString(80, 90, 200, 16, 16, (u8*)buffer);
    
    // ��ʾʱ����������
    POINT_COLOR = BLUE;
    LCD_ShowString(40, 120, 200, 16, 16, (u8*)"Time:");
    
    // ���ݱ༭ģʽ��ʾʱ��
    if (g_TimeEditMode == 1) {
        // �༭Сʱ
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        sprintf(buffer, "%02d", g_TempDateTime.hour);
        LCD_Fill(100, 120, 115, 136, GRAY);
        LCD_ShowString(100, 120, 15, 16, 16, (u8*)buffer);
        
        // ��ʾ���Ӻ�����
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, ":%02d:%02d", g_TempDateTime.minute, g_TempDateTime.second);
        LCD_ShowString(115, 120, 200, 16, 16, (u8*)buffer);
    } else if (g_TimeEditMode == 2) {
        // �༭����
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "%02d:", g_TempDateTime.hour);
        LCD_ShowString(100, 120, 200, 16, 16, (u8*)buffer);
        
        // ������ʾ����
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        sprintf(buffer, "%02d", g_TempDateTime.minute);
        LCD_Fill(118, 120, 133, 136, GRAY);
        LCD_ShowString(118, 120, 15, 16, 16, (u8*)buffer);
        
        // ��ʾ����
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, ":%02d", g_TempDateTime.second);
        LCD_ShowString(133, 120, 200, 16, 16, (u8*)buffer);
    } else if (g_TimeEditMode == 3) {
        // �༭����
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "%02d:%02d:", g_TempDateTime.hour, g_TempDateTime.minute);
        LCD_ShowString(100, 120, 200, 16, 16, (u8*)buffer);
        
        // ������ʾ����
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        sprintf(buffer, "%02d", g_TempDateTime.second);
        LCD_Fill(136, 120, 151, 136, GRAY);
        LCD_ShowString(136, 120, 15, 16, 16, (u8*)buffer);
    } else {
        // δ����༭ģʽ����ͨ��ʾ
        if (g_SelectedItem == 0) {
            // ѡ��״̬
            POINT_COLOR = RED;
            LCD_DrawRectangle(100, 120, 180, 136);
        } else {
            POINT_COLOR = GRAY;
        }
        BACK_COLOR = BLACK;
        sprintf(buffer, "%02d:%02d:%02d", g_TempDateTime.hour, g_TempDateTime.minute, g_TempDateTime.second);
        LCD_ShowString(100, 120, 200, 16, 16, (u8*)buffer);
    }
    
    // ��ʾ���水ť
    if (g_SelectedItem == 1) {
        POINT_COLOR = RED;
        LCD_Fill(20, 160, 100, 190, GRAY);
        BACK_COLOR = GRAY;
    } else {
        POINT_COLOR = RED;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(20, 160, 100, 190);
    LCD_ShowString(40, 168, 200, 16, 16, (u8*)"SAVE");
    
    // ��ʾȡ����ť
    if (g_SelectedItem == 2) {
        POINT_COLOR = RED;
        LCD_Fill(140, 160, 220, 190, GRAY);
        BACK_COLOR = GRAY;
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(140, 160, 220, 190);
    LCD_ShowString(160, 168, 200, 16, 16, (u8*)"CANCEL");
    
    // �ָ�Ĭ�ϱ���ɫ
    BACK_COLOR = BLACK;
    g_NeedRefresh = 0;
}

// ֻˢ�±仯��ʱ�䲿��
void Clock_RefreshTimeDigit(DateTime* time)
{
    char buffer[30];
    POINT_COLOR = RED;
    BACK_COLOR = BLACK;
    
    // ʹ�ø����ļ�࣬��ֹ�ַ��ص�
    // Сʱ: 60-84   ð��1: 84-96   ����: 96-120   ð��2: 120-132   ��: 132-156
    
    // ��������Ƿ�仯
    if (g_LastSecond != time->second) {
        sprintf(buffer, "%02d", time->second);
        // ���ԭ��������Χȷ����ȫ���
        LCD_Fill(132, 110, 156, 134, BLACK);
        LCD_ShowString(132, 110, 40, 24, 24, (u8*)buffer);
        g_LastSecond = time->second;
    }
    
    // �������Ƿ�仯
    if (g_LastMinute != time->minute) {
        sprintf(buffer, "%02d", time->minute);
        // ���ԭ����
        LCD_Fill(96, 110, 120, 134, BLACK);
        LCD_ShowString(96, 110, 40, 24, 24, (u8*)buffer);
        g_LastMinute = time->minute;
        
        // ȷ�����Ӻ���֮���ð����ʾ
        // �����ð������
        LCD_Fill(120, 110, 132, 134, BLACK);
        LCD_ShowString(120, 110, 12, 24, 24, (u8*)":");
    }
    
    // ���Сʱ�Ƿ�仯
    if (g_LastHour != time->hour) {
        sprintf(buffer, "%02d", time->hour);
        // ���ԭ����
        LCD_Fill(60, 110, 84, 134, BLACK);
        LCD_ShowString(60, 110, 40, 24, 24, (u8*)buffer);
        g_LastHour = time->hour;
        
        // ȷ��Сʱ�ͷ���֮���ð����ʾ
        // �����ð������
        LCD_Fill(84, 110, 96, 134, BLACK);
        LCD_ShowString(84, 110, 12, 24, 24, (u8*)":");
    }
    
    // ��������Ƿ�仯
    if (g_LastDay != time->day || g_LastMonth != time->month || g_LastYear != time->year) {
        POINT_COLOR = GRAY;
        sprintf(buffer, "%04d-%02d-%02d", time->year, time->month, time->day);
        LCD_ShowString(70, 50, 200, 16, 16, (u8*)buffer);
        g_LastDay = time->day;
        g_LastMonth = time->month;
        g_LastYear = time->year;
    }
    
    // ��������Ƿ�仯
    if (g_LastWeek != time->week) {
        static const char* weekdays[] = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};
        POINT_COLOR = GRAY;
        LCD_ShowString(85, 75, 200, 16, 16, (u8*)weekdays[time->week]);
        g_LastWeek = time->week;
    }
}

// ������������
void Clock_ProcessKey(uint8_t key)
{
    switch(key) {
        case KEY1_PRESSED:  // PA15 - �����޸İ�ť
            switch(g_CurrentPage) {
                case PAGE_CLOCK:
                    // ��ʱ��ҳ�棬KEY1ֱ�ӽ��������б�ҳ��
                    Clock_ChangePage(PAGE_ALARM_LIST);
                    break;
                    
                case PAGE_ALARM_LIST:
                    // �������б�ҳ�棬ȷ��ѡ��
                    if (g_SelectedItem == 0) {
                        // ����ʱ��ҳ��
                        SelectCallback_GotoClock();
                    } else if (g_SelectedItem == g_AlarmManager.count + 1) {
                        // ��������
                        SelectCallback_AddAlarm();
                    } else if (g_SelectedItem >= 1 && g_SelectedItem <= g_AlarmManager.count) {
                        // �༭����
                        SelectCallback_EditAlarm(g_SelectedItem - 1);
                    }
                    break;
                    
                case PAGE_ALARM_EDIT:
                    // �����ӱ༭ҳ�棬ȷ��ѡ������/�˳��༭ģʽ
                    if (g_SelectedItem == 0) {
                        // ȡ���༭���������и���
                        g_EditingField = 0;
                        g_TimeEditMode = 0;
                        SelectCallback_CancelEdit();
                    } else if (g_SelectedItem == 5) {
                        // ��������
                        g_EditingField = 0;
                        g_TimeEditMode = 0;
                        SelectCallback_SaveAlarm();
                    } else if (g_SelectedItem >= 1 && g_SelectedItem <= 4) {
                        // �����༭�ֶε��߼�
                        if (g_EditingField == 0) {
                            // ����༭ģʽ
                            g_EditingField = g_SelectedItem;
                            g_TimeEditMode = 0; // ����ʱ��༭ģʽ
                            g_NeedRefresh = 1;
                        } else if (g_EditingField == g_SelectedItem) {
                            // ��ǰ���ڱ༭���ֶ�
                            if (g_EditingField == 1) { // ʱ��༭�ֶ�
                                if (g_TimeEditMode == 0) {
                                    // ����Сʱ�༭ģʽ
                                    g_TimeEditMode = 1;
                                } else if (g_TimeEditMode == 1) {
                                    // ��Сʱ�༭�л������ӱ༭
                                    g_TimeEditMode = 2;
                                } else if (g_TimeEditMode == 2) {
                                    // ���ʱ��༭���˳��༭ģʽ
                                    g_TimeEditMode = 0;
                                    g_EditingField = 0;
                                }
                                g_NeedRefresh = 1;
                            } else {
                                // ���������ֶΣ��˳��༭ģʽ
                                g_EditingField = 0;
                                g_NeedRefresh = 1;
                            }
                        } else {
                            // �л����༭�����ֶ�
                            g_EditingField = g_SelectedItem;
                            g_TimeEditMode = 0; // ����ʱ��༭ģʽ
                            g_NeedRefresh = 1;
                        }
                    }
                    break;
                    
                case PAGE_TIME_SET:
                    // ��ʱ������ҳ�洦��ȷ�ϰ�ť
                    if (g_SelectedItem == 0) {
                        // ��ǰ��ʱ������
                        if (g_TimeEditMode == 0) {
                            // ����Сʱ�༭ģʽ
                            g_TimeEditMode = 1;
                        } else if (g_TimeEditMode == 1) {
                            // ��Сʱ�༭�л������ӱ༭
                            g_TimeEditMode = 2;
                        } else if (g_TimeEditMode == 2) {
                            // �ӷ��ӱ༭�л������ӱ༭
                            g_TimeEditMode = 3;
                        } else if (g_TimeEditMode == 3) {
                            // ���ʱ��༭���˳��༭ģʽ
                            g_TimeEditMode = 0;
                        }
                        g_NeedRefresh = 1;
                    } else if (g_SelectedItem == 3) {
                        // ��ǰ����������
                        if (g_TimeEditMode == 0) {
                            // ������ݱ༭ģʽ
                            g_TimeEditMode = 4;
                        } else if (g_TimeEditMode == 4) {
                            // ����ݱ༭�л����·ݱ༭
                            g_TimeEditMode = 5;
                        } else if (g_TimeEditMode == 5) {
                            // ���·ݱ༭�л������ڱ༭
                            g_TimeEditMode = 6;
                        } else if (g_TimeEditMode == 6) {
                            // ������ڱ༭���˳��༭ģʽ
                            g_TimeEditMode = 0;
                            // �Զ��������ڼ�
                            g_TempDateTime.week = GetDayOfWeek(g_TempDateTime.year, g_TempDateTime.month, g_TempDateTime.day);
                        }
                        g_NeedRefresh = 1;
                    } else if (g_SelectedItem == 1) {
                        // ��������
                        SelectCallback_SaveTime();
                    } else if (g_SelectedItem == 2) {
                        // ȡ������
                        SelectCallback_CancelTimeSet();
                    }
                    break;
            }
            break;
            
        case KEY2_PRESSED:  // PC5 - KEY0���ܣ�����ʱ�䵽����/����/�ݼ���ť
            switch(g_CurrentPage) {
                case PAGE_CLOCK:
                    // ��ʱ��ҳ�棬KEY2(KEY0)���͵�ǰʱ�䵽����
                    printf("KEY0���£�����ʱ�䵽����\r\n");
                    UART_Send_Time();
                    break;

                case PAGE_TIME_SET:
                    // �ڱ༭ģʽ��ʹ��PC5�����ݼ�ֵ
                    if (g_TimeEditMode == 1) {
                        // �ݼ�Сʱ
                        if (g_TempDateTime.hour == 0)
                            g_TempDateTime.hour = 23;
                        else
                            g_TempDateTime.hour--;
                        printf("Decreasing hour: %02d\r\n", g_TempDateTime.hour);
                        g_NeedRefresh = 1;
                    } else if (g_TimeEditMode == 2) {
                        // �ݼ�����
                        if (g_TempDateTime.minute == 0)
                            g_TempDateTime.minute = 59;
                        else
                            g_TempDateTime.minute--;
                        printf("Decreasing minute: %02d\r\n", g_TempDateTime.minute);
                        g_NeedRefresh = 1;
                    } else if (g_TimeEditMode == 3) {
                        // �ݼ�����
                        if (g_TempDateTime.second == 0)
                            g_TempDateTime.second = 59;
                        else
                            g_TempDateTime.second--;
                        printf("Decreasing second: %02d\r\n", g_TempDateTime.second);
                        g_NeedRefresh = 1;
                    } else if (g_TimeEditMode == 4) {
                        // �ݼ����
                        if (g_TempDateTime.year == 2000)
                            g_TempDateTime.year = 2099;
                        else
                            g_TempDateTime.year--;
                        printf("Decreasing year: %04d\r\n", g_TempDateTime.year);
                        g_NeedRefresh = 1;
                    } else if (g_TimeEditMode == 5) {
                        // �ݼ��·�
                        if (g_TempDateTime.month == 1)
                            g_TempDateTime.month = 12;
                        else
                            g_TempDateTime.month--;
                        
                        // ��������Ƿ���Ҫ����
                        uint8_t max_days = g_MonthDays[g_TempDateTime.month - 1];
                        if (g_TempDateTime.month == 2 && IsLeapYear(g_TempDateTime.year)) {
                            max_days = 29;
                        }
                        if (g_TempDateTime.day > max_days) {
                            g_TempDateTime.day = max_days;
                        }
                        
                        printf("Decreasing month: %02d\r\n", g_TempDateTime.month);
                        g_NeedRefresh = 1;
                    } else if (g_TimeEditMode == 6) {
                        // �ݼ�����
                        uint8_t max_days = g_MonthDays[g_TempDateTime.month - 1];
                        if (g_TempDateTime.month == 2 && IsLeapYear(g_TempDateTime.year)) {
                            max_days = 29;
                        }
                        
                        if (g_TempDateTime.day == 1)
                            g_TempDateTime.day = max_days;
                        else
                            g_TempDateTime.day--;
                        
                        printf("Decreasing day: %02d\r\n", g_TempDateTime.day);
                        g_NeedRefresh = 1;
                    }
                    break;
                    
                // ����Ҫ������Ϊ����ҳ������PC5���������߼�
            }
            break;
            
        case KEY3_PRESSED:  // PA0 - WK_UP ʱ�������ť
            switch(g_CurrentPage) {
                case PAGE_CLOCK:
                    // ��ʱ��ҳ�棬KEY3(WK_UP)ֱ�ӽ���ʱ������
                    SelectCallback_SetTime();
                    break;
                    
                case PAGE_ALARM_LIST:
                    // �����б�ҳ���ж��ѡ����ذ�ť�������б������Ӱ�ť
                    g_SelectedItem = (g_SelectedItem + 1) % (g_AlarmManager.count + 2);
                    g_NeedRefresh = 1;
                    break;
                    
                case PAGE_ALARM_EDIT:
                    if (g_EditingField == 0) {
                        // δ���ڱ༭״̬��ѡ��ͬ��Ŀ
                        g_SelectedItem = (g_SelectedItem + 1) % 6; // 0-5����6��
                        g_NeedRefresh = 1;
                    } else {
                        // ���ڱ༭״̬���޸ĵ�ǰ��Ŀֵ
                        switch(g_EditingField) {
                            case 1: // �༭ʱ��
                                if (g_TimeEditMode == 1) {
                                    // �༭Сʱ
                                    g_TempAlarm.hour = (g_TempAlarm.hour + 1) % 24;
                                    printf("Editing hour: %02d\r\n", g_TempAlarm.hour);
                                } else if (g_TimeEditMode == 2) {
                                    // �༭����
                                    g_TempAlarm.minute = (g_TempAlarm.minute + 1) % 60;
                                    printf("Editing minute: %02d\r\n", g_TempAlarm.minute);
                                }
                                g_NeedRefresh = 1;
                                break;
                                
                            case 2: // �༭�ظ�
                                // �ڼ���Ԥ��ֵ֮���л�
                                if (g_TempAlarm.days == 0x7F) g_TempAlarm.days = 0x1F; // ÿ�� -> ������
                                else if (g_TempAlarm.days == 0x1F) g_TempAlarm.days = 0x60; // ������ -> ��ĩ
                                else g_TempAlarm.days = 0x7F; // ���� -> ÿ��
                                g_NeedRefresh = 1;
                                break;
                                
                            case 3: // �༭����
                                // �ڼ���Ԥ������֮���л�
                                if (strcmp(g_TempAlarm.name, "Morning") == 0) 
                                    strcpy(g_TempAlarm.name, "Noon");
                                else if (strcmp(g_TempAlarm.name, "Noon") == 0) 
                                    strcpy(g_TempAlarm.name, "Evening");
                                else if (strcmp(g_TempAlarm.name, "Evening") == 0) 
                                    strcpy(g_TempAlarm.name, "Night");
                                else 
                                    strcpy(g_TempAlarm.name, "Morning");
                                g_NeedRefresh = 1;
                                break;
                                
                            case 4: // �༭״̬
                                // �л�����/����״̬
                                g_TempAlarm.enabled = !g_TempAlarm.enabled;
                                g_NeedRefresh = 1;
                                break;
                        }
                    }
                    break;
                    
                case PAGE_TIME_SET:
                    if (g_TimeEditMode == 0) {
                        // δ���ڱ༭״̬�����ĸ�ѡ��֮��ѭ����ʱ�䡢���桢ȡ�������ڣ�
                        g_SelectedItem = (g_SelectedItem + 1) % 4; // 0-3����4��
                        g_NeedRefresh = 1;
                    } else {
                        // ���ڱ༭״̬���޸ĵ�ǰֵ
                        if (g_TimeEditMode == 1) {
                            // �༭Сʱ
                            g_TempDateTime.hour = (g_TempDateTime.hour + 1) % 24;
                            printf("Setting hour: %02d\r\n", g_TempDateTime.hour);
                        } else if (g_TimeEditMode == 2) {
                            // �༭����
                            g_TempDateTime.minute = (g_TempDateTime.minute + 1) % 60;
                            printf("Setting minute: %02d\r\n", g_TempDateTime.minute);
                        } else if (g_TimeEditMode == 3) {
                            // �༭����
                            g_TempDateTime.second = (g_TempDateTime.second + 1) % 60;
                            printf("Setting second: %02d\r\n", g_TempDateTime.second);
                        } else if (g_TimeEditMode == 4) {
                            // �༭���
                            g_TempDateTime.year = (g_TempDateTime.year < 2099) ? g_TempDateTime.year + 1 : 2000;
                            printf("Setting year: %04d\r\n", g_TempDateTime.year);
                        } else if (g_TimeEditMode == 5) {
                            // �༭�·�
                            g_TempDateTime.month = (g_TempDateTime.month % 12) + 1; // 1-12
                            printf("Setting month: %02d\r\n", g_TempDateTime.month);
                            
                            // ��������Ƿ���Ҫ���������磬�����2�£����ڿ�����Ҫ������28/29�죩
                            uint8_t max_days = g_MonthDays[g_TempDateTime.month - 1];
                            if (g_TempDateTime.month == 2 && IsLeapYear(g_TempDateTime.year)) {
                                max_days = 29;
                            }
                            if (g_TempDateTime.day > max_days) {
                                g_TempDateTime.day = max_days;
                            }
                        } else if (g_TimeEditMode == 6) {
                            // �༭����
                            uint8_t max_days = g_MonthDays[g_TempDateTime.month - 1];
                            if (g_TempDateTime.month == 2 && IsLeapYear(g_TempDateTime.year)) {
                                max_days = 29;
                            }
                            g_TempDateTime.day = (g_TempDateTime.day % max_days) + 1; // 1-max_days
                            printf("Setting day: %02d\r\n", g_TempDateTime.day);
                        }
                        g_NeedRefresh = 1;
                    }
                    break;
            }
            break;
    }
}



// ѡ��ص������������б�ҳ��
static void SelectCallback_GotoAlarmList(void)
{
    Clock_ChangePage(PAGE_ALARM_LIST);
}

// ѡ��ص�������ʱ��ҳ��
static void SelectCallback_GotoClock(void)
{
    Clock_ChangePage(PAGE_CLOCK);
}

// ѡ��ص�����������
static void SelectCallback_AddAlarm(void)
{
    g_CurrentAlarmIndex = g_AlarmManager.count;
    g_FirstTimeEnter = 1; // ���ñ�־��ȷ����ȷ��ʼ��������
    Clock_ChangePage(PAGE_ALARM_EDIT);
}

// ѡ��ص����༭����
static void SelectCallback_EditAlarm(uint8_t index)
{
    g_CurrentAlarmIndex = index;
    g_FirstTimeEnter = 1; // ���ñ�־��ȷ�����³�ʼ����ʱ����
    Clock_ChangePage(PAGE_ALARM_EDIT);
}

// ѡ��ص���ɾ������
static void SelectCallback_DeleteAlarm(uint8_t index)
{
    if(Alarm_Delete(&g_AlarmManager, index)) {
        Clock_ChangePage(PAGE_ALARM_LIST);
    }
}

// ѡ��ص�����������
static void SelectCallback_SaveAlarm(void)
{
    printf("Saving alarm settings: Time=%02d:%02d:%02d, Status=%d\r\n", 
           g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second, g_TempAlarm.enabled);
    
    // ���浱ǰ�༭����ʱ����
    if (g_CurrentAlarmIndex < g_AlarmManager.count) {
        // ������������
        g_AlarmManager.alarms[g_CurrentAlarmIndex] = g_TempAlarm;
        printf("Updated alarm[%d] successfully\r\n", g_CurrentAlarmIndex);
    } else {
        // ����������
        if (g_AlarmManager.count < 10) {
            g_AlarmManager.alarms[g_AlarmManager.count] = g_TempAlarm;
            g_AlarmManager.count++;
            printf("Added new alarm successfully, current count: %d\r\n", g_AlarmManager.count);
        }
    }
    
    // �˳��༭ģʽ
    g_EditingField = 0;
    g_TimeEditMode = 0;
    g_FirstTimeEnter = 1; // ���ñ�־���Ա��´α༭ʱ���³�ʼ��
    
    // ���������б�
    Clock_ChangePage(PAGE_ALARM_LIST);
}

// ѡ��ص���ȡ���༭
static void SelectCallback_CancelEdit(void)
{
    // �˳��༭ģʽ
    g_EditingField = 0;
    g_TimeEditMode = 0;
    g_FirstTimeEnter = 1; // ���ñ�־���Ա��´α༭ʱ���³�ʼ��
    
    Clock_ChangePage(PAGE_ALARM_LIST);
}

// ѡ��ص�������ϵͳʱ��
static void SelectCallback_SetTime(void)
{
    g_FirstTimeEnter = 1; // ���ñ�־��ȷ�����뵱ǰʱ��
    Clock_ChangePage(PAGE_TIME_SET);
}

// ѡ��ص�������ϵͳʱ������
static void SelectCallback_SaveTime(void)
{
    printf("Saving system time: %02d:%02d:%02d\r\n", 
           g_TempDateTime.hour, g_TempDateTime.minute, g_TempDateTime.second);
    
    // �������õ�ʱ��
    RTC_SetTime(&g_TempDateTime);
    
    // �˳��༭ģʽ
    g_TimeEditMode = 0;
    g_EditingField = 0;
    g_FirstTimeEnter = 1; // ���ñ�־
    
    // ����ʱ��ҳ��
    Clock_ChangePage(PAGE_CLOCK);
}

// ѡ��ص���ȡ��ϵͳʱ������
static void SelectCallback_CancelTimeSet(void)
{
    // �˳��༭ģʽ
    g_TimeEditMode = 0;
    g_EditingField = 0;
    g_FirstTimeEnter = 1; // ���ñ�־
    
    // ����ʱ��ҳ��
    Clock_ChangePage(PAGE_CLOCK);
}

// ���������ռ������ڼ�������Zeller��ʽ��
uint8_t GetDayOfWeek(uint16_t year, uint8_t month, uint8_t day)
{
    if (month == 1 || month == 2) {
        month += 12;
        year--;
    }

    uint16_t century = year / 100;
    year = year % 100;

    uint8_t week = (day + 13 * (month + 1) / 5 + year + year / 4 + century / 4 - 2 * century) % 7;

    // ת��Ϊ0-6������-������
    if (week == 0) week = 7;
    return (week - 1);
}

// ==================== ������Բ��ʱ�ӻ��ƺ��� ====================

// ����������ģ��ʱ��
void Clock_DrawAnalogClock(DateTime* time)
{
    static uint8_t first_draw = 1;
    static DateTime last_time = {0};

    if (first_draw || g_NeedRefresh) {
        // ���������Ʊ���
        LCD_Clear(WHITE);
        Clock_DrawClockFace();
        first_draw = 0;
        g_NeedRefresh = 0;
        // ǿ���ػ�ָ��
        last_time.second = 255; // ����һ�������ܵ�ֵǿ���ػ�
    }

    // ֻ��ʱ��仯ʱ�ػ�ָ��
    if (last_time.hour != time->hour ||
        last_time.minute != time->minute ||
        last_time.second != time->second) {

        // �����ָ�루�ð�ɫ���ǣ�
        if (last_time.second != 255) { // �����״λ���
            Clock_ClearHands(&last_time);
        }

        // ������ָ��
        Clock_DrawHands(time);

        // ���¼�¼��ʱ��
        last_time = *time;
    }

    // ʵʱ�����·�������ʱ����ʾ
    Clock_DrawDateInfo(time);
}

// ����ʱ�ӱ���
void Clock_DrawClockFace(void)
{
    // ���Ʊ���Ӱ��Ч��
    POINT_COLOR = GRAY;
    LCD_Draw_Circle(CLOCK_CENTER_X + 2, CLOCK_CENTER_Y + 2, CLOCK_RADIUS + 1);

    // ������Ȧ - ��������
    POINT_COLOR = BLACK;
    LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CLOCK_RADIUS);
    LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CLOCK_RADIUS - 1);
    LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CLOCK_RADIUS - 2);

    // ���Ʊ������� - ʹ�ð�ɫ����
    POINT_COLOR = WHITE;
    LCD_Fill(CLOCK_CENTER_X - CLOCK_RADIUS + 3, CLOCK_CENTER_Y - CLOCK_RADIUS + 3,
             CLOCK_CENTER_X + CLOCK_RADIUS - 3, CLOCK_CENTER_Y + CLOCK_RADIUS - 3, WHITE);

    // ���ƾ���Բ
    POINT_COLOR = BLACK;
    LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CLOCK_RADIUS - 3);

    // ����12��Сʱ�̶�
    Clock_DrawHourMarks();

    // ����12������
    Clock_DrawNumbers();

    // ����60�����ӿ̶�
    Clock_DrawMinuteMarks();

    // �������ĵ�
    Clock_DrawCenterDot();
}

// ����Сʱ�̶�
void Clock_DrawHourMarks(void)
{
    int i;
    float angle;
    int x1, y1, x2, y2;

    POINT_COLOR = BLACK;

    for(i = 0; i < 12; i++) {
        angle = i * 30 * M_PI / 180;

        // ��˵�
        x1 = CLOCK_CENTER_X + (CLOCK_RADIUS - 5) * sin(angle);
        y1 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 5) * cos(angle);

        // �ڶ˵�
        x2 = CLOCK_CENTER_X + (CLOCK_RADIUS - 5 - HOUR_MARK_LEN) * sin(angle);
        y2 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 5 - HOUR_MARK_LEN) * cos(angle);

        // ���ƴ̶ֿ���
        DrawThickLine(x1, y1, x2, y2, BLACK, 2);
    }
}

// ���Ʒ��ӿ̶�
void Clock_DrawMinuteMarks(void)
{
    int i;
    float angle;
    int x1, y1, x2, y2;

    POINT_COLOR = GRAY;

    for(i = 0; i < 60; i++) {
        // ����Сʱ�̶�λ��
        if (i % 5 == 0) continue;

        angle = i * 6 * M_PI / 180;

        // ��˵�
        x1 = CLOCK_CENTER_X + (CLOCK_RADIUS - 5) * sin(angle);
        y1 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 5) * cos(angle);

        // �ڶ˵�
        x2 = CLOCK_CENTER_X + (CLOCK_RADIUS - 5 - MINUTE_MARK_LEN) * sin(angle);
        y2 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 5 - MINUTE_MARK_LEN) * cos(angle);

        // ����ϸ�̶���
        LCD_DrawLine(x1, y1, x2, y2);
    }
}

// ����ʱ������
void Clock_DrawNumbers(void)
{
    int i;
    float angle;
    int x, y;
    char num_str[3];

    POINT_COLOR = BLACK;
    BACK_COLOR = WHITE;

    for(i = 1; i <= 12; i++) {
        angle = i * 30 * M_PI / 180;

        // ��������λ�ã��������ֵ���ƫ��
        x = CLOCK_CENTER_X + (CLOCK_RADIUS - 15) * sin(angle);
        y = CLOCK_CENTER_Y - (CLOCK_RADIUS - 15) * cos(angle);

        // ��������λ������X����
        if (i >= 10) {
            x -= 8; // ��λ������ƫ�Ƹ���
        } else {
            x -= 4; // һλ������ƫ��
        }
        y -= 8; // ����ƫ��

        sprintf(num_str, "%d", i);
        LCD_ShowString(x, y, 20, 16, 16, (u8*)num_str);
    }
}

// ����ָ��
void Clock_DrawHand(int16_t angle, uint8_t length, uint16_t color, uint8_t thickness)
{
    float rad = angle * M_PI / 180.0;
    int16_t x = CLOCK_CENTER_X + length * sin(rad);
    int16_t y = CLOCK_CENTER_Y - length * cos(rad);

    if (thickness > 1) {
        DrawThickLine(CLOCK_CENTER_X, CLOCK_CENTER_Y, x, y, color, thickness);
    } else {
        POINT_COLOR = color;
        LCD_DrawLine(CLOCK_CENTER_X, CLOCK_CENTER_Y, x, y);
    }
}

// ����ʱ��ָ�� - �������Ե�����ָ��
void Clock_DrawHands(DateTime* time)
{
    // ����ָ��Ƕȣ�12��Ϊ0�ȣ�˳ʱ�룩
    int16_t hour_angle = ((time->hour % 12) * 30 + time->minute / 2);
    int16_t minute_angle = time->minute * 6;
    int16_t second_angle = time->second * 6;

    // ����ʱ�루�̴ֺ��ߣ�������Ӱ��Ч��
    Clock_DrawHand(hour_angle, HOUR_HAND_LEN, DARKBLUE, 5);
    Clock_DrawHand(hour_angle, HOUR_HAND_LEN, BLUE, 3);

    // ���Ʒ��루�г����ߣ�������Ӱ��Ч��
    Clock_DrawHand(minute_angle, MINUTE_HAND_LEN, DARKBLUE, 4);
    Clock_DrawHand(minute_angle, MINUTE_HAND_LEN, CYAN, 2);

    // �������루��ϸ���ߣ�������Ӱ��Ч��
    Clock_DrawHand(second_angle, SECOND_HAND_LEN, DARKRED, 2);
    Clock_DrawHand(second_angle, SECOND_HAND_LEN, RED, 1);

    // ���»������ĵ㣨ȷ����ָ��֮�ϣ�
    Clock_DrawCenterDot();
}

// �����ָ��
void Clock_ClearHands(DateTime* last_time)
{
    if (last_time->year == 0) return; // �״λ��ƣ��������

    // �����ָ��Ƕ�
    int16_t hour_angle = ((last_time->hour % 12) * 30 + last_time->minute / 2);
    int16_t minute_angle = last_time->minute * 6;
    int16_t second_angle = last_time->second * 6;

    // ʹ�ð�ɫ�����ָ�루ʹ����ͬ�Ĵ�ϸ��
    Clock_DrawHand(hour_angle, HOUR_HAND_LEN, WHITE, 5);
    Clock_DrawHand(minute_angle, MINUTE_HAND_LEN, WHITE, 4);
    Clock_DrawHand(second_angle, SECOND_HAND_LEN, WHITE, 2);

    // ���»��ƿ��ܱ����ǵĿ̶Ⱥ�����
    Clock_DrawHourMarks();
    Clock_DrawMinuteMarks();
    Clock_DrawNumbers();  // ���»��ƿ��ܱ����ǵ�����
}

// ��������Բ��
void Clock_DrawCenterDot(void)
{
    // ���Ʒ�����Ӱ
    POINT_COLOR = GRAY;
    LCD_Fill(CLOCK_CENTER_X - CENTER_DOT_RADIUS + 1,
             CLOCK_CENTER_Y - CENTER_DOT_RADIUS + 1,
             CLOCK_CENTER_X + CENTER_DOT_RADIUS + 1,
             CLOCK_CENTER_Y + CENTER_DOT_RADIUS + 1, GRAY);

    // ���Ʒ���Ȧ
    POINT_COLOR = GOLD;
    LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CENTER_DOT_RADIUS);
    LCD_Fill(CLOCK_CENTER_X - CENTER_DOT_RADIUS + 1,
             CLOCK_CENTER_Y - CENTER_DOT_RADIUS + 1,
             CLOCK_CENTER_X + CENTER_DOT_RADIUS - 1,
             CLOCK_CENTER_Y + CENTER_DOT_RADIUS - 1, GOLD);

    // ���Ʒ���Ȧ�ڲ�
    POINT_COLOR = BLACK;
    LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CENTER_DOT_RADIUS - 1);
}

// �������ں�ʵʱ����ʱ��
void Clock_DrawDateInfo(DateTime* time)
{
    char buffer[50];
    static uint8_t last_second = 255;
    static uint8_t last_day = 255;
    uint16_t text_width;

    // ��ʾ���ڣ�ÿ�ζ���ˢ��ȷ����ʾ
    static uint8_t last_month = 255;
    static uint16_t last_year = 0;
    static uint8_t last_week = 255;

    if (last_day != time->day || last_month != time->month || last_year != time->year ||
        last_week != time->week || g_ForceRefreshDate) {
        // ���Ʊ�������
        LCD_Fill(10, DIGITAL_TIME_Y - 10, 230, DIGITAL_TIME_Y + 50, WHITE);

        // ���Ʒ�����߿�
        POINT_COLOR = LIGHTBLUE;
        LCD_DrawRectangle(20, DIGITAL_TIME_Y - 5, 220, DIGITAL_TIME_Y + 45);
        LCD_DrawRectangle(21, DIGITAL_TIME_Y - 4, 219, DIGITAL_TIME_Y + 44);

        // ��ʾ���� - ���ж���������ʽ
        POINT_COLOR = DARKBLUE;
        BACK_COLOR = WHITE;
        sprintf(buffer, "%04d-%02d-%02d", time->year, time->month, time->day);
        text_width = strlen(buffer) * 8;
        LCD_ShowString(DIGITAL_TIME_X - text_width/2, DIGITAL_TIME_Y, 200, 16, 16, (u8*)buffer);

        // ��ʾ���� - ���ж���
        POINT_COLOR = BLUE;
        sprintf(buffer, "%s", GetWeekdayName(time->week));
        text_width = strlen(buffer) * 8;
        LCD_ShowString(DIGITAL_TIME_X - text_width/2, DIGITAL_TIME_Y + 20, 200, 16, 16, (u8*)buffer);

        last_day = time->day;
        last_month = time->month;
        last_year = time->year;
        last_week = time->week;
        g_ForceRefreshDate = 0;  // ����ǿ��ˢ�±�־
    }

    // ʵʱ��������ʱ�䣨ÿ����£�- ��ʾ��������
    if (last_second != time->second) {
        // ����ɵ�ʱ������
        LCD_Fill(30, 50, 210, 90, WHITE);

        // ���Ʒ�����߿�
        POINT_COLOR = GOLD;
        LCD_DrawRectangle(25, 45, 215, 95);
        LCD_DrawRectangle(26, 46, 214, 94);

        // ��ʾ��ǰʱ�� - ���ж���������ʽ��ͳһ�ֺ�
        POINT_COLOR = RED;
        BACK_COLOR = WHITE;
        sprintf(buffer, "%02d:%02d:%02d", time->hour, time->minute, time->second);
        text_width = 8 * 12;  // ��ȫʱ���ַ������
        LCD_ShowString(DIGITAL_TIME_X - text_width/2, 55, 150, 24, 24, (u8*)buffer);

        last_second = time->second;
    }
}

// ����Ƕ�
int16_t Clock_CalculateAngle(uint8_t value, uint8_t max_value)
{
    return (value * 360) / max_value;
}

// ==================== �������� ====================

// ���ƴ�����
void DrawThickLine(int16_t x1, int16_t y1, int16_t x2, int16_t y2, uint16_t color, uint8_t thickness)
{
    int16_t i, j;
    POINT_COLOR = color;

    for (i = -(thickness/2); i <= thickness/2; i++) {
        for (j = -(thickness/2); j <= thickness/2; j++) {
            LCD_DrawLine(x1 + i, y1 + j, x2 + i, y2 + j);
        }
    }
}

// ��ȡ��������
const char* GetWeekdayName(uint8_t weekday)
{
    if (weekday < 7) {
        return weekday_names[weekday];
    }
    return "Unknown";
}

// ��ȡ�·�����
const char* GetMonthName(uint8_t month)
{
    if (month >= 1 && month <= 12) {
        return month_names[month];
    }
    return "Unknown";
}

// �޸�����ʾ������֧��Բ��ʱ��
void Clock_Display(DateTime* time)
{
    // ���ݵ�ǰҳ����ʾ��ͬ����
    switch(g_CurrentPage) {
        case PAGE_CLOCK:
            // ʹ���µ�Բ��ʱ����ʾ
            Clock_DrawAnalogClock(time);
            // ������Ļ��ť
            Clock_DrawScreenButtons();
            break;
        case PAGE_ALARM_LIST:
            if(g_NeedRefresh) {
                Clock_DrawAlarmList();
                Clock_DrawScreenButtons();
            }
            break;
        case PAGE_ALARM_EDIT:
            if(g_NeedRefresh) {
                Clock_DrawAlarmEdit(g_CurrentAlarmIndex);
                Clock_DrawScreenButtons();
            }
            break;
        case PAGE_TIME_SET:
            if(g_NeedRefresh) {
                Clock_DrawTimeSet(time);
                Clock_DrawScreenButtons();
            }
            break;
    }
}

// ==================== ��Ļ��ť����ʵ�� ====================

// ������Ļ��ť
void Clock_DrawScreenButtons(void)
{
    // ���ư�ť1 (KEY1���� - ��������)
    Clock_DrawButton(BUTTON1_X, BUTTON_Y, BUTTON_WIDTH, BUTTON_HEIGHT,
                    "ALARM", LIGHTBLUE, BLACK);

    // ���ư�ť2 (KEY0���� - ����ʱ�䵽����)
    Clock_DrawButton(BUTTON2_X, BUTTON_Y, BUTTON_WIDTH, BUTTON_HEIGHT,
                    "SEND", LIGHTGREEN, BLACK);

    // ���ư�ť3 (WK_UP���� - ʱ�����)
    Clock_DrawButton(BUTTON3_X, BUTTON_Y, BUTTON_WIDTH, BUTTON_HEIGHT,
                    "TIME", LGRAY, BLACK);
}

// ���Ƶ�����ť
void Clock_DrawButton(uint16_t x, uint16_t y, uint16_t width, uint16_t height,
                     const char* text, uint16_t bg_color, uint16_t text_color)
{
    // ���ư�ť����
    LCD_Fill(x, y, x + width, y + height, bg_color);

    // ���ư�ť�߿�
    POINT_COLOR = BLACK;
    LCD_DrawRectangle(x, y, x + width, y + height);

    // �����ı�����λ��
    uint16_t text_len = strlen(text);
    uint16_t text_x = x + (width - text_len * 8) / 2;  // 8���ؿ��ȹ���
    uint16_t text_y = y + (height - 16) / 2;           // 16���ظ߶�

    // ���ư�ť�ı�
    POINT_COLOR = text_color;
    BACK_COLOR = bg_color;
    LCD_ShowString(text_x, text_y, width, 16, 16, (u8*)text);
}

// ��鰴ť��ѹ (����д�����)
uint8_t Clock_CheckButtonPress(uint16_t touch_x, uint16_t touch_y)
{
    // ��鰴ť1
    if (touch_x >= BUTTON1_X && touch_x <= BUTTON1_X + BUTTON_WIDTH &&
        touch_y >= BUTTON_Y && touch_y <= BUTTON_Y + BUTTON_HEIGHT) {
        return 1; // WK_UP����
    }

    // ��鰴ť2
    if (touch_x >= BUTTON2_X && touch_x <= BUTTON2_X + BUTTON_WIDTH &&
        touch_y >= BUTTON_Y && touch_y <= BUTTON_Y + BUTTON_HEIGHT) {
        return 2; // KEY0����
    }

    // ��鰴ť3
    if (touch_x >= BUTTON3_X && touch_x <= BUTTON3_X + BUTTON_WIDTH &&
        touch_y >= BUTTON_Y && touch_y <= BUTTON_Y + BUTTON_HEIGHT) {
        return 3; // KEY1����
    }

    return 0; // û�а�ť������
}

// ������ť��ѹ
void Clock_ProcessButtonPress(uint8_t button_id)
{
    switch(button_id) {
        case 1: // ģ��KEY1���� - �����޸Ĺ���
            switch(g_CurrentPage) {
                case PAGE_CLOCK:
                    // ��ʱ��ҳ�棬ALARM�����������б� (ģ��KEY1)
                    Clock_ChangePage(PAGE_ALARM_LIST);
                    break;
                default:
                    // ������ҳ�棬ALARM������ҳ���л�
                    g_CurrentPage = (g_CurrentPage + 1) % 4;
                    g_NeedRefresh = 1;
                    break;
            }
            break;

        case 2: // ģ��KEY0���� - ����ʱ�䵽����/ȷ��/ѡ��
            switch(g_CurrentPage) {
                case PAGE_CLOCK:
                    // ��ʱ��ҳ�棬KEY0��ť���͵�ǰʱ�䵽����
                    printf("��ĻKEY0��ť���£�����ʱ�䵽����\r\n");
                    UART_Send_Time();
                    break;
                case PAGE_ALARM_LIST:
                    // �������ӱ༭ҳ��
                    g_CurrentPage = PAGE_ALARM_EDIT;
                    g_NeedRefresh = 1;
                    break;
                case PAGE_ALARM_EDIT:
                    // �����������ò������б�
                    SelectCallback_SaveAlarm();
                    break;
                case PAGE_TIME_SET:
                    // ����ʱ�����ò�������ҳ
                    SelectCallback_SaveTime();
                    break;
            }
            break;

        case 3: // ģ��KEY3���� - WK_UPʱ���������
            switch(g_CurrentPage) {
                case PAGE_CLOCK:
                    // ��ʱ��ҳ�棬SET������ʱ������ (ģ��WK_UP)
                    SelectCallback_SetTime();
                    break;
                case PAGE_ALARM_LIST:
                    // �������б���SET��������ҳ��
                    g_CurrentPage = PAGE_CLOCK;
                    g_NeedRefresh = 1;
                    break;
                case PAGE_ALARM_EDIT:
                    // �����ӱ༭��SET��ȡ���༭
                    SelectCallback_CancelEdit();
                    break;
                case PAGE_TIME_SET:
                    // ��ʱ�����ã�SET��ȡ������
                    SelectCallback_CancelTimeSet();
                    break;
            }
            break;
    }
}

// ==================== �����ĸ������ܺ��� ====================

// ��ʾϵͳ״̬��Ϣ
void Clock_ShowSystemStatus(void)
{
    char buffer[100];

    // �����Ļ����������ʾ״̬
    LCD_Fill(10, 80, 230, 200, WHITE);

    POINT_COLOR = BLUE;
    BACK_COLOR = WHITE;

    // ��ʾϵͳ����ʱ��
    sprintf(buffer, "Uptime: %lu sec", g_TimerCounter / 1000);
    LCD_ShowString(20, 90, 200, 16, 16, (u8*)buffer);

    // ��ʾ����ͳ��
    uint8_t enabled_count = 0;
    for(uint8_t i = 0; i < g_AlarmManager.count; i++) {
        if(g_AlarmManager.alarms[i].enabled) {
            enabled_count++;
        }
    }
    sprintf(buffer, "Alarms: %d/%d (%d ON)", g_AlarmManager.count, MAX_ALARMS, enabled_count);
    LCD_ShowString(20, 110, 200, 16, 16, (u8*)buffer);

    // ��ʾ�ڴ�ʹ��
    sprintf(buffer, "Memory: %d bytes", sizeof(DateTime) + sizeof(AlarmManager));
    LCD_ShowString(20, 130, 200, 16, 16, (u8*)buffer);

    // ��ʾ�汾��Ϣ
    LCD_ShowString(20, 150, 200, 16, 16, (u8*)"Clock System v2.0");

    // 3����Զ����
    delay_ms(3000);
    g_NeedRefresh = 1;
}

// ����������
void Clock_AddNewAlarm(void)
{
    if(g_AlarmManager.count < MAX_ALARMS) {
        Alarm new_alarm;

        // ����Ĭ��ֵ
        new_alarm.hour = 8;
        new_alarm.minute = 0;
        new_alarm.second = 0;
        new_alarm.days = 0x7F; // ÿ�� (bit0-bit6�ֱ�������յ�����)
        new_alarm.enabled = 1;
        sprintf(new_alarm.name, "Alarm %d", g_AlarmManager.count + 1);

        // ���ӵ�������
        if(Alarm_Add(&g_AlarmManager, &new_alarm)) {
            // ��ʾ�ɹ���Ϣ
            LCD_Fill(10, 80, 230, 120, LIGHTGREEN);
            POINT_COLOR = BLACK;
            BACK_COLOR = LIGHTGREEN;
            LCD_ShowString(20, 90, 200, 16, 16, (u8*)"New alarm added!");
            delay_ms(1500);
        }
    } else {
        // ��ʾ������Ϣ
        LCD_Fill(10, 80, 230, 120, RED);
        POINT_COLOR = WHITE;
        BACK_COLOR = RED;
        LCD_ShowString(20, 90, 200, 16, 16, (u8*)"Alarm list full!");
        delay_ms(1500);
    }
}

// �л���ǰ���ӵ�����״̬
void Clock_ToggleAlarmEnabled(void)
{
    if(g_CurrentAlarmIndex < g_AlarmManager.count) {
        g_AlarmManager.alarms[g_CurrentAlarmIndex].enabled =
            !g_AlarmManager.alarms[g_CurrentAlarmIndex].enabled;

        // ��ʾ״̬�仯
        LCD_Fill(10, 80, 230, 120, YELLOW);
        POINT_COLOR = BLACK;
        BACK_COLOR = YELLOW;

        char buffer[50];
        sprintf(buffer, "Alarm %s",
                g_AlarmManager.alarms[g_CurrentAlarmIndex].enabled ? "ENABLED" : "DISABLED");
        LCD_ShowString(20, 90, 200, 16, 16, (u8*)buffer);
        delay_ms(1500);
    }
}

// ����Ϊ��ǰϵͳʱ��
void Clock_ResetToSystemTime(void)
{
    // ����������Ӵ�RTC��ȡ��ʵʱ����߼�
    // Ŀǰ��ʾ����ȷ����Ϣ
    LCD_Fill(10, 80, 230, 120, CYAN);
    POINT_COLOR = BLACK;
    BACK_COLOR = CYAN;
    LCD_ShowString(20, 90, 200, 16, 16, (u8*)"Time reset to RTC");
    delay_ms(1500);
}

// ǿ��ˢ������Ϣ
void Clock_ForceRefreshDateInfo(void)
{
    // ����ǿ��ˢ�±�־��ȷ����һ�ε���Clock_DrawDateInfo ʱ���»��ƻ�����Ϣ
    g_ForceRefreshDate = 1;
}