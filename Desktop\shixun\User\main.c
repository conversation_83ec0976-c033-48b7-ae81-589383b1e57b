#include "stm32f10x.h"
#include "sys.h"
#include "usart.h"
#include "timer.h"
#include "delay.h"
#include "rtc.h"
#include "led.h"
#include "clock_display.h"
#include "key.h"
#include "uart_protocol.h"
#include <string.h>

int main(void)
{
    uint8_t key;

    // 系统初始化
    SystemInit();

    // 时钟初始化
   // SysTick_Init();

    // LED初始化
    LED_Init();

    // 按键初始化
    KEY_Init();

    // 串口初始化
    uart1_init(115200);

    // 实时时钟初始化
    RTC_Init();

    // 定时器初始化
    TIM2_Init(999, 71);  // 1ms定时器 (72MHz / (71+1) / (999+1) = 1000Hz)
    TIM3_Init(499, 7199); // 500ms定时器 (72MHz / (7199+1) / (499+1) = 2Hz)
    TIM4_Init();  // 初始化TIM4用于delay_us和delay_ms函数

    // 串口通信协议初始化
    UART_Protocol_Init();

    // 显示初始化
    Clock_Init();

    // 手动清空闹钟列表
    memset(&g_AlarmManager, 0, sizeof(g_AlarmManager));
    
    // ���Ӷ��������������֤LED��˸�ʹ��ڷ��͹���
    Alarm alarm;

    // ����1: 30��󴥷�
    alarm.hour = 0;
    alarm.minute = 0;
    alarm.second = 30;
    alarm.enabled = 1;
    alarm.days = 0x7F; // ÿ��
    strcpy(alarm.name, "Test Alarm 30s");
    Alarm_Add(&g_AlarmManager, &alarm);

    // ����2: 1���Ӻ󴥷�
    alarm.hour = 0;
    alarm.minute = 1;
    alarm.second = 0;
    alarm.enabled = 1;
    alarm.days = 0x7F; // ÿ��
    strcpy(alarm.name, "Test Alarm 1min");
    Alarm_Add(&g_AlarmManager, &alarm);

    // ����3: 1��30��󴥷�
    alarm.hour = 0;
    alarm.minute = 1;
    alarm.second = 30;
    alarm.enabled = 1;
    alarm.days = 0x7F; // ÿ��
    strcpy(alarm.name, "Test Alarm 1m30s");
    Alarm_Add(&g_AlarmManager, &alarm);
    
    // ��ӡϵͳ��ʼ�������Ϣ
    printf("Clock system initialized\r\n");
    printf("Current time: %04d-%02d-%02d %02d:%02d:%02d\r\n",
           g_DateTime.year, g_DateTime.month, g_DateTime.day,
           g_DateTime.hour, g_DateTime.minute, g_DateTime.second);

    // ��ʾ��ǰ������Ϣ
    printf("Alarm list (%d alarms):\r\n", g_AlarmManager.count);
    for(uint8_t i=0; i<g_AlarmManager.count; i++) {
        Alarm* a = &g_AlarmManager.alarms[i];
        printf("[%d] %02d:%02d:%02d %s Status:%s Repeat:0x%02X\r\n", 
               i, a->hour, a->minute, a->second, 
               a->name, a->enabled ? "ON" : "OFF", a->days);
    }
    
    while(1)
    {
        // 按键检测处理
        key = KEY_Scan(0);
        if(key != KEY_NONE) {
            // 打印按键信息到串口调试
            switch(key) {
                case KEY1_PRESSED:
                    printf("KEY1按下: 闹钟功能\r\n");
                    break;
                case KEY2_PRESSED:
                    printf("KEY0按下: 发送时间到串口\r\n");
                    break;
                case KEY3_PRESSED:
                    printf("WK_UP按下: 时间设置\r\n");
                    break;
                default:
                    printf("未知按键: %d\r\n", key);
                    break;
            }
            Clock_ProcessKey(key);
        }

        // 显示时钟
        Clock_Display(&g_DateTime);

        // 处理串口通信协议
        UART_Protocol_Process();

        // 处理自动对时功能
        UART_AutoTime_Process();

        // 延时10ms，降低CPU占用
        delay_ms(10);
    }
}
