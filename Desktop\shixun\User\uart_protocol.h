#ifndef __UART_PROTOCOL_H
#define __UART_PROTOCOL_H

#include "stm32f10x.h"
#include "rtc.h"
#include "usart.h"

// Maximum number of alarms
#define MAX_ALARMS        10

// Protocol command definitions
#define CMD_GET_TIME        0x01  // Get current time
#define CMD_SET_TIME        0x02  // Set current time
#define CMD_GET_ALARM       0x03  // Get alarm information
#define CMD_SET_ALARM       0x04  // Set alarm information
#define CMD_HELP            0x05  // Get command help information
#define CMD_LIST_ALARMS     0x06  // List all alarms
#define CMD_DELETE_ALARM    0x07  // Delete specified alarm
#define CMD_ENABLE_ALARM    0x08  // Enable specified alarm
#define CMD_DISABLE_ALARM   0x09  // Disable specified alarm
#define CMD_GET_STATUS      0x0A  // Get system status
#define CMD_START_AUTO_TIME 0x0B  // Start automatic time transmission
#define CMD_STOP_AUTO_TIME  0x0C  // Stop automatic time transmission

// Protocol frame structure
typedef struct {
    uint8_t cmd;             // Command code
    uint8_t data_len;        // Data length
    uint8_t data[32];        // Data content, maximum 32 bytes
} UartProtocolFrame;

// 函数声明
void UART_Protocol_Init(void);
void UART_Protocol_Process(void);
void UART_Send_Time(void);
void UART_Send_Alarm(uint8_t index);
void UART_Send_Help(void);
void UART_Send_AlarmList(void);
void UART_Send_Status(void);

// Protocol parsing related functions
uint8_t UART_Parse_Command(char *cmd_str);
void UART_Parse_Time(char *time_str);
void UART_Parse_Alarm(char *alarm_str);
void UART_Delete_Alarm(uint8_t index);
void UART_Enable_Alarm(uint8_t index);
void UART_Disable_Alarm(uint8_t index);
void UART_Start_AutoTime(void);
void UART_Stop_AutoTime(void);
void UART_AutoTime_Process(void);
#endif /* __UART_PROTOCOL_H */ 
