#ifndef __TOUCH_H
#define __TOUCH_H

#include "stm32f10x.h"

// 触摸屏相关定义
#define TOUCH_SPI                SPI1
#define TOUCH_SPI_CLK            RCC_APB2Periph_SPI1

// 触摸屏SPI引脚定义
#define TOUCH_CS_PIN             GPIO_Pin_4
#define TOUCH_CS_PORT            GPIOA
#define TOUCH_CS_CLK             RCC_APB2Periph_GPIOA

#define TOUCH_SCK_PIN            GPIO_Pin_5
#define TOUCH_SCK_PORT           GPIOA

#define TOUCH_MISO_PIN           GPIO_Pin_6
#define TOUCH_MISO_PORT          GPIOA

#define TOUCH_MOSI_PIN           GPIO_Pin_7
#define TOUCH_MOSI_PORT          GPIOA

#define TOUCH_IRQ_PIN            GPIO_Pin_1
#define TOUCH_IRQ_PORT           GPIOB
#define TOUCH_IRQ_CLK            RCC_APB2Periph_GPIOB

// 触摸屏控制引脚操作
#define TOUCH_CS_HIGH()          GPIO_SetBits(TOUCH_CS_PORT, TOUCH_CS_PIN)
#define TOUCH_CS_LOW()           GPIO_ResetBits(TOUCH_CS_PORT, TOUCH_CS_PIN)
#define TOUCH_IRQ_READ()         GPIO_ReadInputDataBit(TOUCH_IRQ_PORT, TOUCH_IRQ_PIN)

// 触摸屏命令定义
#define TOUCH_CMD_X              0x90  // 读取X坐标
#define TOUCH_CMD_Y              0xD0  // 读取Y坐标

// 触摸屏校准参数
typedef struct {
    uint16_t x_min;
    uint16_t x_max;
    uint16_t y_min;
    uint16_t y_max;
    uint16_t lcd_width;
    uint16_t lcd_height;
} TouchCalibration;

// 触摸点结构体
typedef struct {
    uint16_t x;
    uint16_t y;
    uint8_t pressed;
} TouchPoint;

// 全局变量声明
extern TouchCalibration g_TouchCal;
extern TouchPoint g_TouchPoint;

// 函数声明
void Touch_Init(void);
uint8_t Touch_Scan(TouchPoint* point);
uint16_t Touch_ReadAD(uint8_t cmd);
void Touch_Calibrate(void);
void Touch_SetCalibration(uint16_t x_min, uint16_t x_max, uint16_t y_min, uint16_t y_max);
uint8_t Touch_GetPoint(TouchPoint* point);
void Touch_DrawCross(uint16_t x, uint16_t y, uint16_t color);

// SPI相关函数
void Touch_SPI_Init(void);
uint8_t Touch_SPI_ReadWrite(uint8_t data);

#endif /* __TOUCH_H */
