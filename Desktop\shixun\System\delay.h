/**
 * @file    delay.h
 * @brief   Delay functions header file for STM32F10x
 * <AUTHOR>
 * @date    2025-03-27
 */

#ifndef DELAY_H
#define DELAY_H

#include <stdint.h>  // 标准整数类型定义

// 函数声明

/**
 * @brief  系统时钟初始化
 * @param  None
 * @retval None
 */
void SysTick_Init(void);

/**
 * @brief  TIM3初始化函数
 * @param  None
 * @retval None
 */
void TIM3_Init(void);

/**
 * @brief  微秒级延时函数
 * @param  time: 延时时间(微秒)
 * @retval None
 */
void delay_us(uint16_t time);

/**
 * @brief  毫秒级延时函数
 * @param  time: 延时时间(毫秒)
 * @retval None
 */
void delay_ms(uint16_t time);

#endif // DELAY_H
