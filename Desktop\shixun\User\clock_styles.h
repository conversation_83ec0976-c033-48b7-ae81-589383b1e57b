#ifndef __CLOCK_STYLES_H
#define __CLOCK_STYLES_H

#include "stm32f10x.h"
#include "lcd.h"
#include "rtc.h"

// 样式定义
#define STYLE_MODERN    0  // 现代简约风格
#define STYLE_CLASSIC   1  // 经典复古风格

// 现代简约风格配色方案
#define MODERN_BG_COLOR      WHITE        // 白色背景
#define MODERN_TEXT_COLOR    0x2104       // 深灰色文字 #333333
#define MODERN_ACCENT_COLOR  0x001F       // 蓝色强调 #007AFF
#define MODERN_LIGHT_COLOR   0xF7BE       // 浅灰色 #F5F5F5
#define MODERN_WARNING_COLOR 0xFD20       // 橙色警告 #FF9500
#define MODERN_SUCCESS_COLOR 0x07E0       // 绿色成功
#define MODERN_CARD_COLOR    0xFFFF       // 卡片背景色

// 经典复古风格配色方案
#define CLASSIC_BG_COLOR     0x18C3       // 米色背景 #F5F5DC
#define CLASSIC_TEXT_COLOR   0x4208       // 深棕色文字 #8B4513
#define CLASSIC_ACCENT_COLOR 0x8800       // 金色强调 #FFD700
#define CLASSIC_LIGHT_COLOR  0xEF5D       // 浅棕色 #DEB887
#define CLASSIC_WARNING_COLOR 0xA800      // 深红色警告 #B22222
#define CLASSIC_SUCCESS_COLOR 0x2589      // 深绿色成功
#define CLASSIC_WOOD_COLOR   0x6B4D       // 木色边框

// 布局参数
#define CARD_MARGIN         10
#define CARD_RADIUS         8
#define BUTTON_RADIUS       6
#define ICON_SIZE           16

// 现代简约风格布局
#define MODERN_TIME_Y       80
#define MODERN_DATE_Y       40
#define MODERN_BUTTON_Y     200
#define MODERN_CARD_HEIGHT  50

// 经典复古风格布局
#define CLASSIC_CLOCK_Y     120
#define CLASSIC_INFO_Y      250
#define CLASSIC_BUTTON_Y    280

// 全局样式变量
extern uint8_t g_CurrentStyle;

// 样式管理函数
void Clock_SetStyle(uint8_t style);
uint8_t Clock_GetStyle(void);
void Clock_InitStyle(void);

// 现代简约风格绘制函数
void Clock_DrawModernMain(DateTime* time);
void Clock_DrawModernCard(int16_t x, int16_t y, int16_t width, int16_t height, const char* title);
void Clock_DrawModernButton(int16_t x, int16_t y, int16_t width, int16_t height, 
                           const char* text, uint8_t selected);
void Clock_DrawModernAlarmList(void);
void Clock_DrawModernAlarmEdit(uint8_t index);
void Clock_DrawModernTimeSet(DateTime* time);

// 经典复古风格绘制函数
void Clock_DrawClassicMain(DateTime* time);
void Clock_DrawClassicAnalogClock(DateTime* time);
void Clock_DrawClassicWoodFrame(int16_t x, int16_t y, int16_t width, int16_t height);
void Clock_DrawClassicButton(int16_t x, int16_t y, int16_t width, int16_t height, 
                            const char* text, uint8_t selected);
void Clock_DrawClassicAlarmList(void);
void Clock_DrawClassicAlarmEdit(uint8_t index);
void Clock_DrawClassicTimeSet(DateTime* time);

// 通用辅助绘制函数
void DrawRoundedRect(int16_t x1, int16_t y1, int16_t x2, int16_t y2, 
                     uint16_t color, uint8_t radius);
void DrawCard(int16_t x, int16_t y, int16_t width, int16_t height, 
              uint16_t bg_color, uint16_t border_color);
void DrawGradientRect(int16_t x1, int16_t y1, int16_t x2, int16_t y2, 
                      uint16_t color1, uint16_t color2);
void DrawShadow(int16_t x, int16_t y, int16_t width, int16_t height);

// 图标绘制函数
void DrawIcon_Clock(int16_t x, int16_t y, uint16_t color);
void DrawIcon_Alarm(int16_t x, int16_t y, uint16_t color);
void DrawIcon_Settings(int16_t x, int16_t y, uint16_t color);
void DrawIcon_Back(int16_t x, int16_t y, uint16_t color);

// 颜色获取函数
uint16_t GetBgColor(void);
uint16_t GetTextColor(void);
uint16_t GetAccentColor(void);
uint16_t GetLightColor(void);
uint16_t GetWarningColor(void);

#endif /* __CLOCK_STYLES_H */
